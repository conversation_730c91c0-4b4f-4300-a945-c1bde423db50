{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\inventory\\\\Inventory.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Package, Plus, Search, AlertTriangle, Calendar, Edit, Trash2, RefreshCw, Upload } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useProducts } from '../../hooks/useProducts';\nimport ProductModal from './ProductModal';\nimport InventoryStats from './InventoryStats';\nimport StockAdjustmentModal from './StockAdjustmentModal';\nimport LowStockAlert from './LowStockAlert';\nimport BulkOperations from './BulkOperations';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Inventory = () => {\n  _s();\n  const {\n    hasPermission\n  } = useAuth();\n  const {\n    products,\n    loading,\n    error,\n    updateProduct,\n    deleteProduct,\n    updateStock,\n    getLowStockProducts,\n    getExpiringProducts,\n    getProductCategories\n  } = useProducts();\n  const [activeView, setActiveView] = useState('products');\n  const [showProductModal, setShowProductModal] = useState(false);\n  const [showStockModal, setShowStockModal] = useState(false);\n  const [showBulkOperations, setShowBulkOperations] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [adjustingStock, setAdjustingStock] = useState(null);\n  const [selectedProducts, setSelectedProducts] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [stockFilter, setStockFilter] = useState('all');\n\n  // Filter products based on search, category, and stock status\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !selectedCategory || product.category === selectedCategory;\n    let matchesStock = true;\n    switch (stockFilter) {\n      case 'low':\n        matchesStock = product.stockQuantity <= product.reorderLevel && product.stockQuantity > 0;\n        break;\n      case 'out':\n        matchesStock = product.stockQuantity === 0;\n        break;\n      case 'expiring':\n        const thirtyDaysFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\n        matchesStock = Boolean(product.hasExpiry && product.expiryDate && product.expiryDate <= thirtyDaysFromNow);\n        break;\n    }\n    return matchesSearch && matchesCategory && matchesStock;\n  });\n  const categories = getProductCategories();\n  const lowStockProducts = getLowStockProducts();\n  const expiringProducts = getExpiringProducts();\n  const handleEditProduct = product => {\n    setEditingProduct(product);\n    setShowProductModal(true);\n  };\n  const handleDeleteProduct = async productId => {\n    if (window.confirm('Are you sure you want to delete this product?')) {\n      try {\n        await deleteProduct(productId);\n      } catch (error) {\n        console.error('Error deleting product:', error);\n      }\n    }\n  };\n  const handleStockAdjustment = product => {\n    setAdjustingStock(product);\n    setShowStockModal(true);\n  };\n  const handleToggleActive = async product => {\n    try {\n      await updateProduct(product.id, {\n        isActive: !product.isActive\n      });\n    } catch (error) {\n      console.error('Error toggling product status:', error);\n    }\n  };\n  const handleProductSelect = (product, isSelected) => {\n    if (isSelected) {\n      setSelectedProducts(prev => [...prev, product]);\n    } else {\n      setSelectedProducts(prev => prev.filter(p => p.id !== product.id));\n    }\n  };\n  const handleSelectAll = () => {\n    if (selectedProducts.length === filteredProducts.length) {\n      setSelectedProducts([]);\n    } else {\n      setSelectedProducts(filteredProducts);\n    }\n  };\n  const handleClearSelection = () => {\n    setSelectedProducts([]);\n  };\n  const handleOpenBulkOperations = () => {\n    setShowBulkOperations(true);\n  };\n  if (!hasPermission(['admin', 'attendant'])) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(Package, {\n        className: \"mx-auto h-12 w-12 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"mt-2 text-sm font-medium text-gray-900\",\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-500\",\n        children: \"You don't have permission to access inventory management.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Package, {\n            className: \"h-6 w-6 text-primary-600 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Inventory Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex bg-gray-100 rounded-lg p-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveView('products'),\n              className: `px-3 py-1 rounded text-sm font-medium transition-colors ${activeView === 'products' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n              children: \"Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveView('stats'),\n              className: `px-3 py-1 rounded text-sm font-medium transition-colors ${activeView === 'stats' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n              children: \"Statistics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveView('alerts'),\n              className: `px-3 py-1 rounded text-sm font-medium transition-colors ${activeView === 'alerts' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n              children: [\"Alerts\", (lowStockProducts.length > 0 || expiringProducts.length > 0) && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5\",\n                children: lowStockProducts.length + expiringProducts.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), hasPermission('admin') && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleOpenBulkOperations,\n              className: \"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Upload, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this), \"Import Excel\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setEditingProduct(null);\n                setShowProductModal(true);\n              },\n              className: \"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Plus, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this), \"Add Product\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this), activeView === 'products' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row gap-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 relative\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search products...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: e => setSelectedCategory(e.target.value),\n            className: \"px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category,\n              children: category\n            }, category, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: stockFilter,\n            onChange: e => setStockFilter(e.target.value),\n            className: \"px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"low\",\n              children: \"Low Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"out\",\n              children: \"Out of Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"expiring\",\n              children: \"Expiring Soon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), activeView === 'stats' ? /*#__PURE__*/_jsxDEV(InventoryStats, {\n      products: products\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 9\n    }, this) : activeView === 'alerts' ? /*#__PURE__*/_jsxDEV(LowStockAlert, {\n      lowStockProducts: lowStockProducts,\n      expiringProducts: expiringProducts,\n      onStockAdjust: handleStockAdjustment\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg p-6\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-gray-500\",\n            children: \"Loading products...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 15\n        }, this) : filteredProducts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(Package, {\n            className: \"mx-auto h-12 w-12 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"mt-2 text-sm font-medium text-gray-900\",\n            children: \"No products found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-gray-500\",\n            children: searchTerm || selectedCategory || stockFilter !== 'all' ? 'Try adjusting your search or filter criteria.' : 'Get started by adding your first product.'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n          children: filteredProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n            product: product,\n            onEdit: handleEditProduct,\n            onDelete: handleDeleteProduct,\n            onStockAdjust: handleStockAdjustment,\n            onToggleActive: handleToggleActive,\n            canEdit: hasPermission('admin')\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 11\n      }, this)\n    }, void 0, false), showProductModal && /*#__PURE__*/_jsxDEV(ProductModal, {\n      product: editingProduct,\n      onClose: () => {\n        setShowProductModal(false);\n        setEditingProduct(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 9\n    }, this), showStockModal && adjustingStock && /*#__PURE__*/_jsxDEV(StockAdjustmentModal, {\n      product: adjustingStock,\n      onClose: () => {\n        setShowStockModal(false);\n        setAdjustingStock(null);\n      },\n      onAdjust: updateStock\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 9\n    }, this), showBulkOperations && /*#__PURE__*/_jsxDEV(BulkOperations, {\n      selectedProducts: selectedProducts,\n      onClose: () => {\n        setShowBulkOperations(false);\n        setSelectedProducts([]);\n      },\n      onClearSelection: handleClearSelection\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this);\n};\n\n// Product Card Component\n_s(Inventory, \"qme2XvGLmqtHWmRpMTsNDeH501w=\", false, function () {\n  return [useAuth, useProducts];\n});\n_c = Inventory;\nconst ProductCard = ({\n  product,\n  onEdit,\n  onDelete,\n  onStockAdjust,\n  onToggleActive,\n  canEdit\n}) => {\n  const isLowStock = product.stockQuantity <= product.reorderLevel;\n  const isOutOfStock = product.stockQuantity === 0;\n  const isExpiringSoon = product.hasExpiry && product.expiryDate && product.expiryDate <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\n  const getStockStatusColor = () => {\n    if (isOutOfStock) return 'text-red-600 bg-red-100';\n    if (isLowStock) return 'text-orange-600 bg-orange-100';\n    return 'text-green-600 bg-green-100';\n  };\n  const getStockStatusText = () => {\n    if (isOutOfStock) return 'Out of Stock';\n    if (isLowStock) return 'Low Stock';\n    return 'In Stock';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-white border rounded-lg p-4 ${product.isActive ? 'border-gray-200' : 'border-gray-300 bg-gray-50'}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start justify-between mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold ${product.isActive ? 'text-gray-900' : 'text-gray-500'}`,\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-sm ${product.isActive ? 'text-gray-600' : 'text-gray-400'}`,\n          children: product.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), canEdit && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onEdit(product),\n          className: \"text-blue-600 hover:text-blue-800\",\n          children: /*#__PURE__*/_jsxDEV(Edit, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onDelete(product.id),\n          className: \"text-red-600 hover:text-red-800\",\n          children: /*#__PURE__*/_jsxDEV(Trash2, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\",\n          children: product.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-semibold text-green-600\",\n          children: [\"KSh \", product.price.toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Stock Level:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStockStatusColor()}`,\n              children: getStockStatusText()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onStockAdjust(product),\n              className: \"text-blue-600 hover:text-blue-800\",\n              title: \"Adjust Stock\",\n              children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n                className: \"h-3 w-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Current:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `font-medium ${isOutOfStock ? 'text-red-600' : isLowStock ? 'text-orange-600' : 'text-green-600'}`,\n            children: [product.stockQuantity, \" units\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Reorder Level:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium text-gray-900\",\n            children: [product.reorderLevel, \" units\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this), product.hasExpiry && product.expiryDate && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-600\",\n          children: \"Expires:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `font-medium ${isExpiringSoon ? 'text-orange-600' : 'text-gray-900'}`,\n            children: product.expiryDate.toLocaleDateString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this), isExpiringSoon && /*#__PURE__*/_jsxDEV(Calendar, {\n            className: \"h-3 w-3 text-orange-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 34\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 11\n      }, this), canEdit && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between pt-2 border-t\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Status:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onToggleActive(product),\n          className: `inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${product.isActive ? 'bg-green-100 text-green-800 hover:bg-green-200' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}`,\n          children: product.isActive ? 'Active' : 'Inactive'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 11\n      }, this), (isLowStock || isExpiringSoon) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pt-2 border-t\",\n        children: [isLowStock && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center text-xs text-orange-600 mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            className: \"h-3 w-3 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 17\n          }, this), \"Stock below reorder level\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 15\n        }, this), isExpiringSoon && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center text-xs text-orange-600\",\n          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n            className: \"h-3 w-3 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 17\n          }, this), \"Expires within 30 days\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 382,\n    columnNumber: 5\n  }, this);\n};\n_c2 = ProductCard;\nexport default Inventory;\nvar _c, _c2;\n$RefreshReg$(_c, \"Inventory\");\n$RefreshReg$(_c2, \"ProductCard\");", "map": {"version": 3, "names": ["React", "useState", "Package", "Plus", "Search", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Calendar", "Edit", "Trash2", "RefreshCw", "Upload", "useAuth", "useProducts", "ProductModal", "InventoryStats", "StockAdjustmentModal", "LowStockAlert", "BulkOperations", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Inventory", "_s", "hasPermission", "products", "loading", "error", "updateProduct", "deleteProduct", "updateStock", "getLowStockProducts", "getExpiringProducts", "getProductCategories", "activeView", "setActiveView", "showProductModal", "setShowProductModal", "showStockModal", "setShowStockModal", "showBulkOperations", "setShowBulkOperations", "editingProduct", "setEditingProduct", "adjustingStock", "setAdjustingStock", "selectedProducts", "setSelectedProducts", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "stockFilter", "setStockFilter", "filteredProducts", "filter", "product", "matchesSearch", "name", "toLowerCase", "includes", "description", "matchesCategory", "category", "matchesStock", "stockQuantity", "reorderLevel", "thirtyDaysFromNow", "Date", "now", "Boolean", "hasEx<PERSON>ry", "expiryDate", "categories", "lowStockProducts", "expiringProducts", "handleEditProduct", "handleDeleteProduct", "productId", "window", "confirm", "console", "handleStockAdjustment", "handleToggleActive", "id", "isActive", "handleProductSelect", "isSelected", "prev", "p", "handleSelectAll", "length", "handleClearSelection", "handleOpenBulkOperations", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "placeholder", "value", "onChange", "e", "target", "map", "onStockAdjust", "ProductCard", "onEdit", "onDelete", "onToggleActive", "canEdit", "onClose", "onAdjust", "onClearSelection", "_c", "isLowStock", "isOutOfStock", "isExpiringSoon", "getStockStatusColor", "getStockStatusText", "price", "toLocaleString", "title", "toLocaleDateString", "_c2", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/inventory/Inventory.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Package,\n  Plus,\n  Search,\n  Filter,\n  AlertTriangle,\n  Calendar,\n  TrendingDown,\n  BarChart3,\n  Edit,\n  Trash2,\n  RefreshCw,\n  Download,\n  Upload\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useProducts } from '../../hooks/useProducts';\nimport { Product } from '../../types';\nimport ProductModal from './ProductModal';\nimport InventoryStats from './InventoryStats';\nimport StockAdjustmentModal from './StockAdjustmentModal';\nimport LowStockAlert from './LowStockAlert';\nimport BulkOperations from './BulkOperations';\n\nconst Inventory: React.FC = () => {\n  const { hasPermission } = useAuth();\n  const {\n    products,\n    loading,\n    error,\n    updateProduct,\n    deleteProduct,\n    updateStock,\n    getLowStockProducts,\n    getExpiringProducts,\n    getProductCategories\n  } = useProducts();\n\n  const [activeView, setActiveView] = useState<'products' | 'stats' | 'alerts'>('products');\n  const [showProductModal, setShowProductModal] = useState(false);\n  const [showStockModal, setShowStockModal] = useState(false);\n  const [showBulkOperations, setShowBulkOperations] = useState(false);\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null);\n  const [adjustingStock, setAdjustingStock] = useState<Product | null>(null);\n  const [selectedProducts, setSelectedProducts] = useState<Product[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [stockFilter, setStockFilter] = useState<'all' | 'low' | 'out' | 'expiring'>('all');\n\n  // Filter products based on search, category, and stock status\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         product.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !selectedCategory || product.category === selectedCategory;\n\n    let matchesStock = true;\n    switch (stockFilter) {\n      case 'low':\n        matchesStock = product.stockQuantity <= product.reorderLevel && product.stockQuantity > 0;\n        break;\n      case 'out':\n        matchesStock = product.stockQuantity === 0;\n        break;\n      case 'expiring':\n        const thirtyDaysFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\n        matchesStock = Boolean(product.hasExpiry && product.expiryDate && product.expiryDate <= thirtyDaysFromNow);\n        break;\n    }\n\n    return matchesSearch && matchesCategory && matchesStock;\n  });\n\n  const categories = getProductCategories();\n  const lowStockProducts = getLowStockProducts();\n  const expiringProducts = getExpiringProducts();\n\n  const handleEditProduct = (product: Product) => {\n    setEditingProduct(product);\n    setShowProductModal(true);\n  };\n\n  const handleDeleteProduct = async (productId: string) => {\n    if (window.confirm('Are you sure you want to delete this product?')) {\n      try {\n        await deleteProduct(productId);\n      } catch (error) {\n        console.error('Error deleting product:', error);\n      }\n    }\n  };\n\n  const handleStockAdjustment = (product: Product) => {\n    setAdjustingStock(product);\n    setShowStockModal(true);\n  };\n\n  const handleToggleActive = async (product: Product) => {\n    try {\n      await updateProduct(product.id, { isActive: !product.isActive });\n    } catch (error) {\n      console.error('Error toggling product status:', error);\n    }\n  };\n\n  const handleProductSelect = (product: Product, isSelected: boolean) => {\n    if (isSelected) {\n      setSelectedProducts(prev => [...prev, product]);\n    } else {\n      setSelectedProducts(prev => prev.filter(p => p.id !== product.id));\n    }\n  };\n\n  const handleSelectAll = () => {\n    if (selectedProducts.length === filteredProducts.length) {\n      setSelectedProducts([]);\n    } else {\n      setSelectedProducts(filteredProducts);\n    }\n  };\n\n  const handleClearSelection = () => {\n    setSelectedProducts([]);\n  };\n\n  const handleOpenBulkOperations = () => {\n    setShowBulkOperations(true);\n  };\n\n  if (!hasPermission(['admin', 'attendant'])) {\n    return (\n      <div className=\"text-center py-12\">\n        <Package className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Access Denied</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          You don't have permission to access inventory management.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <div className=\"flex items-center\">\n            <Package className=\"h-6 w-6 text-primary-600 mr-2\" />\n            <h1 className=\"text-2xl font-bold text-gray-900\">Inventory Management</h1>\n          </div>\n\n          <div className=\"flex items-center space-x-3\">\n            {/* View Toggle */}\n            <div className=\"flex bg-gray-100 rounded-lg p-1\">\n              <button\n                onClick={() => setActiveView('products')}\n                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${\n                  activeView === 'products'\n                    ? 'bg-white text-gray-900 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                Products\n              </button>\n              <button\n                onClick={() => setActiveView('stats')}\n                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${\n                  activeView === 'stats'\n                    ? 'bg-white text-gray-900 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                Statistics\n              </button>\n              <button\n                onClick={() => setActiveView('alerts')}\n                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${\n                  activeView === 'alerts'\n                    ? 'bg-white text-gray-900 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                Alerts\n                {(lowStockProducts.length > 0 || expiringProducts.length > 0) && (\n                  <span className=\"ml-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5\">\n                    {lowStockProducts.length + expiringProducts.length}\n                  </span>\n                )}\n              </button>\n            </div>\n\n            {hasPermission('admin') && (\n              <div className=\"flex items-center space-x-3\">\n                <button\n                  onClick={handleOpenBulkOperations}\n                  className=\"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center\"\n                >\n                  <Upload className=\"h-4 w-4 mr-2\" />\n                  Import Excel\n                </button>\n                <button\n                  onClick={() => {\n                    setEditingProduct(null);\n                    setShowProductModal(true);\n                  }}\n                  className=\"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center\"\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Product\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\">\n            {error}\n          </div>\n        )}\n\n        {/* Search and Filters - Only show for products view */}\n        {activeView === 'products' && (\n          <div className=\"flex flex-col lg:flex-row gap-4 mb-6\">\n            <div className=\"flex-1 relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search products...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n              />\n            </div>\n\n            <div className=\"flex gap-3\">\n              <select\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n              >\n                <option value=\"\">All Categories</option>\n                {categories.map(category => (\n                  <option key={category} value={category}>{category}</option>\n                ))}\n              </select>\n\n              <select\n                value={stockFilter}\n                onChange={(e) => setStockFilter(e.target.value as any)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n              >\n                <option value=\"all\">All Stock</option>\n                <option value=\"low\">Low Stock</option>\n                <option value=\"out\">Out of Stock</option>\n                <option value=\"expiring\">Expiring Soon</option>\n              </select>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Content based on active view */}\n      {activeView === 'stats' ? (\n        <InventoryStats products={products} />\n      ) : activeView === 'alerts' ? (\n        <LowStockAlert\n          lowStockProducts={lowStockProducts}\n          expiringProducts={expiringProducts}\n          onStockAdjust={handleStockAdjustment}\n        />\n      ) : (\n        <>\n          {/* Products Grid */}\n          <div className=\"bg-white shadow rounded-lg p-6\">\n            {loading ? (\n              <div className=\"text-center py-8\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"></div>\n                <p className=\"mt-2 text-gray-500\">Loading products...</p>\n              </div>\n            ) : filteredProducts.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <Package className=\"mx-auto h-12 w-12 text-gray-400\" />\n                <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No products found</h3>\n                <p className=\"mt-1 text-sm text-gray-500\">\n                  {searchTerm || selectedCategory || stockFilter !== 'all'\n                    ? 'Try adjusting your search or filter criteria.'\n                    : 'Get started by adding your first product.'}\n                </p>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {filteredProducts.map((product) => (\n                  <ProductCard\n                    key={product.id}\n                    product={product}\n                    onEdit={handleEditProduct}\n                    onDelete={handleDeleteProduct}\n                    onStockAdjust={handleStockAdjustment}\n                    onToggleActive={handleToggleActive}\n                    canEdit={hasPermission('admin')}\n                  />\n                ))}\n              </div>\n            )}\n          </div>\n        </>\n      )}\n\n      {/* Modals */}\n      {showProductModal && (\n        <ProductModal\n          product={editingProduct}\n          onClose={() => {\n            setShowProductModal(false);\n            setEditingProduct(null);\n          }}\n        />\n      )}\n\n      {showStockModal && adjustingStock && (\n        <StockAdjustmentModal\n          product={adjustingStock}\n          onClose={() => {\n            setShowStockModal(false);\n            setAdjustingStock(null);\n          }}\n          onAdjust={updateStock}\n        />\n      )}\n\n      {showBulkOperations && (\n        <BulkOperations\n          selectedProducts={selectedProducts}\n          onClose={() => {\n            setShowBulkOperations(false);\n            setSelectedProducts([]);\n          }}\n          onClearSelection={handleClearSelection}\n        />\n      )}\n    </div>\n  );\n};\n\n// Product Card Component\ninterface ProductCardProps {\n  product: Product;\n  onEdit: (product: Product) => void;\n  onDelete: (productId: string) => void;\n  onStockAdjust: (product: Product) => void;\n  onToggleActive: (product: Product) => void;\n  canEdit: boolean;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({\n  product,\n  onEdit,\n  onDelete,\n  onStockAdjust,\n  onToggleActive,\n  canEdit\n}) => {\n  const isLowStock = product.stockQuantity <= product.reorderLevel;\n  const isOutOfStock = product.stockQuantity === 0;\n  const isExpiringSoon = product.hasExpiry && product.expiryDate &&\n    product.expiryDate <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\n\n  const getStockStatusColor = () => {\n    if (isOutOfStock) return 'text-red-600 bg-red-100';\n    if (isLowStock) return 'text-orange-600 bg-orange-100';\n    return 'text-green-600 bg-green-100';\n  };\n\n  const getStockStatusText = () => {\n    if (isOutOfStock) return 'Out of Stock';\n    if (isLowStock) return 'Low Stock';\n    return 'In Stock';\n  };\n\n  return (\n    <div className={`bg-white border rounded-lg p-4 ${product.isActive ? 'border-gray-200' : 'border-gray-300 bg-gray-50'}`}>\n      <div className=\"flex items-start justify-between mb-3\">\n        <div className=\"flex-1\">\n          <h3 className={`font-semibold ${product.isActive ? 'text-gray-900' : 'text-gray-500'}`}>\n            {product.name}\n          </h3>\n          <p className={`text-sm ${product.isActive ? 'text-gray-600' : 'text-gray-400'}`}>\n            {product.description}\n          </p>\n        </div>\n        {canEdit && (\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={() => onEdit(product)}\n              className=\"text-blue-600 hover:text-blue-800\"\n            >\n              <Edit className=\"h-4 w-4\" />\n            </button>\n            <button\n              onClick={() => onDelete(product.id)}\n              className=\"text-red-600 hover:text-red-800\"\n            >\n              <Trash2 className=\"h-4 w-4\" />\n            </button>\n          </div>\n        )}\n      </div>\n\n      <div className=\"space-y-3\">\n        {/* Category and Price */}\n        <div className=\"flex items-center justify-between\">\n          <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">\n            {product.category}\n          </span>\n          <span className=\"font-semibold text-green-600\">\n            KSh {product.price.toLocaleString()}\n          </span>\n        </div>\n\n        {/* Stock Information */}\n        <div className=\"space-y-2\">\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-sm text-gray-600\">Stock Level:</span>\n            <div className=\"flex items-center space-x-2\">\n              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStockStatusColor()}`}>\n                {getStockStatusText()}\n              </span>\n              <button\n                onClick={() => onStockAdjust(product)}\n                className=\"text-blue-600 hover:text-blue-800\"\n                title=\"Adjust Stock\"\n              >\n                <RefreshCw className=\"h-3 w-3\" />\n              </button>\n            </div>\n          </div>\n\n          <div className=\"flex items-center justify-between text-sm\">\n            <span className=\"text-gray-600\">Current:</span>\n            <span className={`font-medium ${isOutOfStock ? 'text-red-600' : isLowStock ? 'text-orange-600' : 'text-green-600'}`}>\n              {product.stockQuantity} units\n            </span>\n          </div>\n\n          <div className=\"flex items-center justify-between text-sm\">\n            <span className=\"text-gray-600\">Reorder Level:</span>\n            <span className=\"font-medium text-gray-900\">{product.reorderLevel} units</span>\n          </div>\n        </div>\n\n        {/* Expiry Information */}\n        {product.hasExpiry && product.expiryDate && (\n          <div className=\"flex items-center justify-between text-sm\">\n            <span className=\"text-gray-600\">Expires:</span>\n            <div className=\"flex items-center space-x-1\">\n              <span className={`font-medium ${isExpiringSoon ? 'text-orange-600' : 'text-gray-900'}`}>\n                {product.expiryDate.toLocaleDateString()}\n              </span>\n              {isExpiringSoon && <Calendar className=\"h-3 w-3 text-orange-600\" />}\n            </div>\n          </div>\n        )}\n\n        {/* Status Toggle */}\n        {canEdit && (\n          <div className=\"flex items-center justify-between pt-2 border-t\">\n            <span className=\"text-sm text-gray-600\">Status:</span>\n            <button\n              onClick={() => onToggleActive(product)}\n              className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${\n                product.isActive\n                  ? 'bg-green-100 text-green-800 hover:bg-green-200'\n                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'\n              }`}\n            >\n              {product.isActive ? 'Active' : 'Inactive'}\n            </button>\n          </div>\n        )}\n\n        {/* Alerts */}\n        {(isLowStock || isExpiringSoon) && (\n          <div className=\"pt-2 border-t\">\n            {isLowStock && (\n              <div className=\"flex items-center text-xs text-orange-600 mb-1\">\n                <AlertTriangle className=\"h-3 w-3 mr-1\" />\n                Stock below reorder level\n              </div>\n            )}\n            {isExpiringSoon && (\n              <div className=\"flex items-center text-xs text-orange-600\">\n                <Calendar className=\"h-3 w-3 mr-1\" />\n                Expires within 30 days\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Inventory;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,OAAO,EACPC,IAAI,EACJC,MAAM,EAENC,aAAa,EACbC,QAAQ,EAGRC,IAAI,EACJC,MAAM,EACNC,SAAS,EAETC,MAAM,QACD,cAAc;AACrB,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,WAAW,QAAQ,yBAAyB;AAErD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAc,CAAC,GAAGb,OAAO,CAAC,CAAC;EACnC,MAAM;IACJc,QAAQ;IACRC,OAAO;IACPC,KAAK;IACLC,aAAa;IACbC,aAAa;IACbC,WAAW;IACXC,mBAAmB;IACnBC,mBAAmB;IACnBC;EACF,CAAC,GAAGrB,WAAW,CAAC,CAAC;EAEjB,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAkC,UAAU,CAAC;EACzF,MAAM,CAACmC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAAC2C,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAAC6C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9C,QAAQ,CAAY,EAAE,CAAC;EACvE,MAAM,CAAC+C,UAAU,EAAEC,aAAa,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAqC,KAAK,CAAC;;EAEzF;EACA,MAAMqD,gBAAgB,GAAG7B,QAAQ,CAAC8B,MAAM,CAACC,OAAO,IAAI;IAClD,MAAMC,aAAa,GAAGD,OAAO,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACZ,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC,IAC9DH,OAAO,CAACK,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACZ,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC;IACzF,MAAMG,eAAe,GAAG,CAACZ,gBAAgB,IAAIM,OAAO,CAACO,QAAQ,KAAKb,gBAAgB;IAElF,IAAIc,YAAY,GAAG,IAAI;IACvB,QAAQZ,WAAW;MACjB,KAAK,KAAK;QACRY,YAAY,GAAGR,OAAO,CAACS,aAAa,IAAIT,OAAO,CAACU,YAAY,IAAIV,OAAO,CAACS,aAAa,GAAG,CAAC;QACzF;MACF,KAAK,KAAK;QACRD,YAAY,GAAGR,OAAO,CAACS,aAAa,KAAK,CAAC;QAC1C;MACF,KAAK,UAAU;QACb,MAAME,iBAAiB,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QACzEL,YAAY,GAAGM,OAAO,CAACd,OAAO,CAACe,SAAS,IAAIf,OAAO,CAACgB,UAAU,IAAIhB,OAAO,CAACgB,UAAU,IAAIL,iBAAiB,CAAC;QAC1G;IACJ;IAEA,OAAOV,aAAa,IAAIK,eAAe,IAAIE,YAAY;EACzD,CAAC,CAAC;EAEF,MAAMS,UAAU,GAAGxC,oBAAoB,CAAC,CAAC;EACzC,MAAMyC,gBAAgB,GAAG3C,mBAAmB,CAAC,CAAC;EAC9C,MAAM4C,gBAAgB,GAAG3C,mBAAmB,CAAC,CAAC;EAE9C,MAAM4C,iBAAiB,GAAIpB,OAAgB,IAAK;IAC9Cb,iBAAiB,CAACa,OAAO,CAAC;IAC1BnB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMwC,mBAAmB,GAAG,MAAOC,SAAiB,IAAK;IACvD,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnE,IAAI;QACF,MAAMnD,aAAa,CAACiD,SAAS,CAAC;MAChC,CAAC,CAAC,OAAOnD,KAAK,EAAE;QACdsD,OAAO,CAACtD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;IACF;EACF,CAAC;EAED,MAAMuD,qBAAqB,GAAI1B,OAAgB,IAAK;IAClDX,iBAAiB,CAACW,OAAO,CAAC;IAC1BjB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM4C,kBAAkB,GAAG,MAAO3B,OAAgB,IAAK;IACrD,IAAI;MACF,MAAM5B,aAAa,CAAC4B,OAAO,CAAC4B,EAAE,EAAE;QAAEC,QAAQ,EAAE,CAAC7B,OAAO,CAAC6B;MAAS,CAAC,CAAC;IAClE,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACdsD,OAAO,CAACtD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED,MAAM2D,mBAAmB,GAAGA,CAAC9B,OAAgB,EAAE+B,UAAmB,KAAK;IACrE,IAAIA,UAAU,EAAE;MACdxC,mBAAmB,CAACyC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEhC,OAAO,CAAC,CAAC;IACjD,CAAC,MAAM;MACLT,mBAAmB,CAACyC,IAAI,IAAIA,IAAI,CAACjC,MAAM,CAACkC,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAK5B,OAAO,CAAC4B,EAAE,CAAC,CAAC;IACpE;EACF,CAAC;EAED,MAAMM,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI5C,gBAAgB,CAAC6C,MAAM,KAAKrC,gBAAgB,CAACqC,MAAM,EAAE;MACvD5C,mBAAmB,CAAC,EAAE,CAAC;IACzB,CAAC,MAAM;MACLA,mBAAmB,CAACO,gBAAgB,CAAC;IACvC;EACF,CAAC;EAED,MAAMsC,oBAAoB,GAAGA,CAAA,KAAM;IACjC7C,mBAAmB,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAM8C,wBAAwB,GAAGA,CAAA,KAAM;IACrCpD,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,IAAI,CAACjB,aAAa,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,EAAE;IAC1C,oBACEL,OAAA;MAAK2E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC5E,OAAA,CAACjB,OAAO;QAAC4F,SAAS,EAAC;MAAiC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvDhF,OAAA;QAAI2E,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzEhF,OAAA;QAAG2E,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEV;EAEA,oBACEhF,OAAA;IAAK2E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB5E,OAAA;MAAK2E,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7C5E,OAAA;QAAK2E,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5E,OAAA;UAAK2E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5E,OAAA,CAACjB,OAAO;YAAC4F,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDhF,OAAA;YAAI2E,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eAENhF,OAAA;UAAK2E,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAE1C5E,OAAA;YAAK2E,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9C5E,OAAA;cACEiF,OAAO,EAAEA,CAAA,KAAMjE,aAAa,CAAC,UAAU,CAAE;cACzC2D,SAAS,EAAE,2DACT5D,UAAU,KAAK,UAAU,GACrB,kCAAkC,GAClC,mCAAmC,EACtC;cAAA6D,QAAA,EACJ;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThF,OAAA;cACEiF,OAAO,EAAEA,CAAA,KAAMjE,aAAa,CAAC,OAAO,CAAE;cACtC2D,SAAS,EAAE,2DACT5D,UAAU,KAAK,OAAO,GAClB,kCAAkC,GAClC,mCAAmC,EACtC;cAAA6D,QAAA,EACJ;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThF,OAAA;cACEiF,OAAO,EAAEA,CAAA,KAAMjE,aAAa,CAAC,QAAQ,CAAE;cACvC2D,SAAS,EAAE,2DACT5D,UAAU,KAAK,QAAQ,GACnB,kCAAkC,GAClC,mCAAmC,EACtC;cAAA6D,QAAA,GACJ,QAEC,EAAC,CAACrB,gBAAgB,CAACiB,MAAM,GAAG,CAAC,IAAIhB,gBAAgB,CAACgB,MAAM,GAAG,CAAC,kBAC1DxE,OAAA;gBAAM2E,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC5ErB,gBAAgB,CAACiB,MAAM,GAAGhB,gBAAgB,CAACgB;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEL3E,aAAa,CAAC,OAAO,CAAC,iBACrBL,OAAA;YAAK2E,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C5E,OAAA;cACEiF,OAAO,EAAEP,wBAAyB;cAClCC,SAAS,EAAC,qFAAqF;cAAAC,QAAA,gBAE/F5E,OAAA,CAACT,MAAM;gBAACoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThF,OAAA;cACEiF,OAAO,EAAEA,CAAA,KAAM;gBACbzD,iBAAiB,CAAC,IAAI,CAAC;gBACvBN,mBAAmB,CAAC,IAAI,CAAC;cAC3B,CAAE;cACFyD,SAAS,EAAC,uFAAuF;cAAAC,QAAA,gBAEjG5E,OAAA,CAAChB,IAAI;gBAAC2F,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELxE,KAAK,iBACJR,OAAA;QAAK2E,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjFpE;MAAK;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAjE,UAAU,KAAK,UAAU,iBACxBf,OAAA;QAAK2E,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD5E,OAAA;UAAK2E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B5E,OAAA,CAACf,MAAM;YAAC0F,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/FhF,OAAA;YACEkF,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,oBAAoB;YAChCC,KAAK,EAAEvD,UAAW;YAClBwD,QAAQ,EAAGC,CAAC,IAAKxD,aAAa,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CT,SAAS,EAAC;UAA0G;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENhF,OAAA;UAAK2E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB5E,OAAA;YACEoF,KAAK,EAAErD,gBAAiB;YACxBsD,QAAQ,EAAGC,CAAC,IAAKtD,mBAAmB,CAACsD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACrDT,SAAS,EAAC,6FAA6F;YAAAC,QAAA,gBAEvG5E,OAAA;cAAQoF,KAAK,EAAC,EAAE;cAAAR,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACvC1B,UAAU,CAACkC,GAAG,CAAC5C,QAAQ,iBACtB5C,OAAA;cAAuBoF,KAAK,EAAExC,QAAS;cAAAgC,QAAA,EAAEhC;YAAQ,GAApCA,QAAQ;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqC,CAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEThF,OAAA;YACEoF,KAAK,EAAEnD,WAAY;YACnBoD,QAAQ,EAAGC,CAAC,IAAKpD,cAAc,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAY,CAAE;YACvDT,SAAS,EAAC,6FAA6F;YAAAC,QAAA,gBAEvG5E,OAAA;cAAQoF,KAAK,EAAC,KAAK;cAAAR,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtChF,OAAA;cAAQoF,KAAK,EAAC,KAAK;cAAAR,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtChF,OAAA;cAAQoF,KAAK,EAAC,KAAK;cAAAR,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzChF,OAAA;cAAQoF,KAAK,EAAC,UAAU;cAAAR,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLjE,UAAU,KAAK,OAAO,gBACrBf,OAAA,CAACL,cAAc;MAACW,QAAQ,EAAEA;IAAS;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GACpCjE,UAAU,KAAK,QAAQ,gBACzBf,OAAA,CAACH,aAAa;MACZ0D,gBAAgB,EAAEA,gBAAiB;MACnCC,gBAAgB,EAAEA,gBAAiB;MACnCiC,aAAa,EAAE1B;IAAsB;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,gBAEFhF,OAAA,CAAAE,SAAA;MAAA0E,QAAA,eAEE5E,OAAA;QAAK2E,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAC5CrE,OAAO,gBACNP,OAAA;UAAK2E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B5E,OAAA;YAAK2E,SAAS,EAAC;UAAyE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/FhF,OAAA;YAAG2E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,GACJ7C,gBAAgB,CAACqC,MAAM,KAAK,CAAC,gBAC/BxE,OAAA;UAAK2E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5E,OAAA,CAACjB,OAAO;YAAC4F,SAAS,EAAC;UAAiC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvDhF,OAAA;YAAI2E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7EhF,OAAA;YAAG2E,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACtC/C,UAAU,IAAIE,gBAAgB,IAAIE,WAAW,KAAK,KAAK,GACpD,+CAA+C,GAC/C;UAA2C;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,gBAENhF,OAAA;UAAK2E,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEzC,gBAAgB,CAACqD,GAAG,CAAEnD,OAAO,iBAC5BrC,OAAA,CAAC0F,WAAW;YAEVrD,OAAO,EAAEA,OAAQ;YACjBsD,MAAM,EAAElC,iBAAkB;YAC1BmC,QAAQ,EAAElC,mBAAoB;YAC9B+B,aAAa,EAAE1B,qBAAsB;YACrC8B,cAAc,EAAE7B,kBAAmB;YACnC8B,OAAO,EAAEzF,aAAa,CAAC,OAAO;UAAE,GAN3BgC,OAAO,CAAC4B,EAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOhB,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC,gBACN,CACH,EAGA/D,gBAAgB,iBACfjB,OAAA,CAACN,YAAY;MACX2C,OAAO,EAAEd,cAAe;MACxBwE,OAAO,EAAEA,CAAA,KAAM;QACb7E,mBAAmB,CAAC,KAAK,CAAC;QAC1BM,iBAAiB,CAAC,IAAI,CAAC;MACzB;IAAE;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEA7D,cAAc,IAAIM,cAAc,iBAC/BzB,OAAA,CAACJ,oBAAoB;MACnByC,OAAO,EAAEZ,cAAe;MACxBsE,OAAO,EAAEA,CAAA,KAAM;QACb3E,iBAAiB,CAAC,KAAK,CAAC;QACxBM,iBAAiB,CAAC,IAAI,CAAC;MACzB,CAAE;MACFsE,QAAQ,EAAErF;IAAY;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CACF,EAEA3D,kBAAkB,iBACjBrB,OAAA,CAACF,cAAc;MACb6B,gBAAgB,EAAEA,gBAAiB;MACnCoE,OAAO,EAAEA,CAAA,KAAM;QACbzE,qBAAqB,CAAC,KAAK,CAAC;QAC5BM,mBAAmB,CAAC,EAAE,CAAC;MACzB,CAAE;MACFqE,gBAAgB,EAAExB;IAAqB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA5E,EAAA,CAhUMD,SAAmB;EAAA,QACGX,OAAO,EAW7BC,WAAW;AAAA;AAAAyG,EAAA,GAZX/F,SAAmB;AA0UzB,MAAMuF,WAAuC,GAAGA,CAAC;EAC/CrD,OAAO;EACPsD,MAAM;EACNC,QAAQ;EACRH,aAAa;EACbI,cAAc;EACdC;AACF,CAAC,KAAK;EACJ,MAAMK,UAAU,GAAG9D,OAAO,CAACS,aAAa,IAAIT,OAAO,CAACU,YAAY;EAChE,MAAMqD,YAAY,GAAG/D,OAAO,CAACS,aAAa,KAAK,CAAC;EAChD,MAAMuD,cAAc,GAAGhE,OAAO,CAACe,SAAS,IAAIf,OAAO,CAACgB,UAAU,IAC5DhB,OAAO,CAACgB,UAAU,IAAI,IAAIJ,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAEvE,MAAMoD,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIF,YAAY,EAAE,OAAO,yBAAyB;IAClD,IAAID,UAAU,EAAE,OAAO,+BAA+B;IACtD,OAAO,6BAA6B;EACtC,CAAC;EAED,MAAMI,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIH,YAAY,EAAE,OAAO,cAAc;IACvC,IAAID,UAAU,EAAE,OAAO,WAAW;IAClC,OAAO,UAAU;EACnB,CAAC;EAED,oBACEnG,OAAA;IAAK2E,SAAS,EAAE,kCAAkCtC,OAAO,CAAC6B,QAAQ,GAAG,iBAAiB,GAAG,4BAA4B,EAAG;IAAAU,QAAA,gBACtH5E,OAAA;MAAK2E,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD5E,OAAA;QAAK2E,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrB5E,OAAA;UAAI2E,SAAS,EAAE,iBAAiBtC,OAAO,CAAC6B,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;UAAAU,QAAA,EACpFvC,OAAO,CAACE;QAAI;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACLhF,OAAA;UAAG2E,SAAS,EAAE,WAAWtC,OAAO,CAAC6B,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;UAAAU,QAAA,EAC7EvC,OAAO,CAACK;QAAW;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EACLc,OAAO,iBACN9F,OAAA;QAAK2E,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C5E,OAAA;UACEiF,OAAO,EAAEA,CAAA,KAAMU,MAAM,CAACtD,OAAO,CAAE;UAC/BsC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7C5E,OAAA,CAACZ,IAAI;YAACuF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACThF,OAAA;UACEiF,OAAO,EAAEA,CAAA,KAAMW,QAAQ,CAACvD,OAAO,CAAC4B,EAAE,CAAE;UACpCU,SAAS,EAAC,iCAAiC;UAAAC,QAAA,eAE3C5E,OAAA,CAACX,MAAM;YAACsF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENhF,OAAA;MAAK2E,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExB5E,OAAA;QAAK2E,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD5E,OAAA;UAAM2E,SAAS,EAAC,oFAAoF;UAAAC,QAAA,EACjGvC,OAAO,CAACO;QAAQ;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACPhF,OAAA;UAAM2E,SAAS,EAAC,8BAA8B;UAAAC,QAAA,GAAC,MACzC,EAACvC,OAAO,CAACmE,KAAK,CAACC,cAAc,CAAC,CAAC;QAAA;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNhF,OAAA;QAAK2E,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB5E,OAAA;UAAK2E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD5E,OAAA;YAAM2E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3DhF,OAAA;YAAK2E,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C5E,OAAA;cAAM2E,SAAS,EAAE,4DAA4D2B,mBAAmB,CAAC,CAAC,EAAG;cAAA1B,QAAA,EAClG2B,kBAAkB,CAAC;YAAC;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACPhF,OAAA;cACEiF,OAAO,EAAEA,CAAA,KAAMQ,aAAa,CAACpD,OAAO,CAAE;cACtCsC,SAAS,EAAC,mCAAmC;cAC7C+B,KAAK,EAAC,cAAc;cAAA9B,QAAA,eAEpB5E,OAAA,CAACV,SAAS;gBAACqF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhF,OAAA;UAAK2E,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBACxD5E,OAAA;YAAM2E,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/ChF,OAAA;YAAM2E,SAAS,EAAE,eAAeyB,YAAY,GAAG,cAAc,GAAGD,UAAU,GAAG,iBAAiB,GAAG,gBAAgB,EAAG;YAAAvB,QAAA,GACjHvC,OAAO,CAACS,aAAa,EAAC,QACzB;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENhF,OAAA;UAAK2E,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBACxD5E,OAAA;YAAM2E,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrDhF,OAAA;YAAM2E,SAAS,EAAC,2BAA2B;YAAAC,QAAA,GAAEvC,OAAO,CAACU,YAAY,EAAC,QAAM;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL3C,OAAO,CAACe,SAAS,IAAIf,OAAO,CAACgB,UAAU,iBACtCrD,OAAA;QAAK2E,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxD5E,OAAA;UAAM2E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/ChF,OAAA;UAAK2E,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C5E,OAAA;YAAM2E,SAAS,EAAE,eAAe0B,cAAc,GAAG,iBAAiB,GAAG,eAAe,EAAG;YAAAzB,QAAA,EACpFvC,OAAO,CAACgB,UAAU,CAACsD,kBAAkB,CAAC;UAAC;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,EACNqB,cAAc,iBAAIrG,OAAA,CAACb,QAAQ;YAACwF,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAc,OAAO,iBACN9F,OAAA;QAAK2E,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAC9D5E,OAAA;UAAM2E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtDhF,OAAA;UACEiF,OAAO,EAAEA,CAAA,KAAMY,cAAc,CAACxD,OAAO,CAAE;UACvCsC,SAAS,EAAE,yEACTtC,OAAO,CAAC6B,QAAQ,GACZ,gDAAgD,GAChD,6CAA6C,EAChD;UAAAU,QAAA,EAEFvC,OAAO,CAAC6B,QAAQ,GAAG,QAAQ,GAAG;QAAU;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGA,CAACmB,UAAU,IAAIE,cAAc,kBAC5BrG,OAAA;QAAK2E,SAAS,EAAC,eAAe;QAAAC,QAAA,GAC3BuB,UAAU,iBACTnG,OAAA;UAAK2E,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7D5E,OAAA,CAACd,aAAa;YAACyF,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,EACAqB,cAAc,iBACbrG,OAAA;UAAK2E,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBACxD5E,OAAA,CAACb,QAAQ;YAACwF,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,0BAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC4B,GAAA,GAlJIlB,WAAuC;AAoJ7C,eAAevF,SAAS;AAAC,IAAA+F,EAAA,EAAAU,GAAA;AAAAC,YAAA,CAAAX,EAAA;AAAAW,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
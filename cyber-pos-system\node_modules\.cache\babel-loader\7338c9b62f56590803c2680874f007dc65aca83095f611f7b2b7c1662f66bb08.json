{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\debug\\\\FirebaseConnectionTest.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { testFirebaseConnection, testServicesQuery } from '../../utils/firebaseConnectionTest';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FirebaseConnectionTest = () => {\n  _s();\n  const [connectionResult, setConnectionResult] = useState(null);\n  const [servicesResult, setServicesResult] = useState(null);\n  const [testing, setTesting] = useState(false);\n  const runConnectionTest = async () => {\n    setTesting(true);\n    try {\n      const result = await testFirebaseConnection();\n      setConnectionResult(result);\n    } catch (error) {\n      setConnectionResult({\n        success: false,\n        message: `Test failed: ${error}`\n      });\n    }\n    setTesting(false);\n  };\n  const runServicesTest = async () => {\n    setTesting(true);\n    try {\n      const result = await testServicesQuery();\n      setServicesResult(result);\n    } catch (error) {\n      setServicesResult({\n        success: false,\n        message: `Test failed: ${error}`\n      });\n    }\n    setTesting(false);\n  };\n  const runAllTests = async () => {\n    await runConnectionTest();\n    await runServicesTest();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 bg-white rounded-lg shadow-md\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-bold mb-4\",\n      children: \"Firebase Connection Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: runConnectionTest,\n          disabled: testing,\n          className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50\",\n          children: \"Test Connection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: runServicesTest,\n          disabled: testing,\n          className: \"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50\",\n          children: \"Test Services Query\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: runAllTests,\n          disabled: testing,\n          className: \"px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50\",\n          children: \"Run All Tests\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), testing && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-blue-600\",\n        children: \"\\uD83D\\uDD04 Running tests...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this), connectionResult && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-4 rounded ${connectionResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-semibold\",\n          children: \"Connection Test Result:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: connectionResult.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this), connectionResult.details && /*#__PURE__*/_jsxDEV(\"pre\", {\n          className: \"mt-2 text-sm\",\n          children: JSON.stringify(connectionResult.details, null, 2)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this), servicesResult && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-4 rounded ${servicesResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-semibold\",\n          children: \"Services Query Test Result:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: servicesResult.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this), servicesResult.servicesCount !== undefined && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1\",\n          children: [\"Services found: \", servicesResult.servicesCount]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(FirebaseConnectionTest, \"RkLlhNeMOfDc3M6qKYeNBbwxn+c=\");\n_c = FirebaseConnectionTest;\nexport default FirebaseConnectionTest;\nvar _c;\n$RefreshReg$(_c, \"FirebaseConnectionTest\");", "map": {"version": 3, "names": ["React", "useState", "testFirebaseConnection", "testServicesQuery", "jsxDEV", "_jsxDEV", "FirebaseConnectionTest", "_s", "connectionResult", "setConnectionResult", "servicesResult", "setServicesResult", "testing", "setTesting", "runConnectionTest", "result", "error", "success", "message", "runServicesTest", "runAllTests", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "details", "JSON", "stringify", "servicesCount", "undefined", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/debug/FirebaseConnectionTest.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { testFirebaseConnection, testServicesQuery } from '../../utils/firebaseConnectionTest';\n\ninterface TestResult {\n  success: boolean;\n  message: string;\n  details?: any;\n  servicesCount?: number;\n}\n\nconst FirebaseConnectionTest: React.FC = () => {\n  const [connectionResult, setConnectionResult] = useState<TestResult | null>(null);\n  const [servicesResult, setServicesResult] = useState<TestResult | null>(null);\n  const [testing, setTesting] = useState(false);\n\n  const runConnectionTest = async () => {\n    setTesting(true);\n    try {\n      const result = await testFirebaseConnection();\n      setConnectionResult(result);\n    } catch (error) {\n      setConnectionResult({\n        success: false,\n        message: `Test failed: ${error}`,\n      });\n    }\n    setTesting(false);\n  };\n\n  const runServicesTest = async () => {\n    setTesting(true);\n    try {\n      const result = await testServicesQuery();\n      setServicesResult(result);\n    } catch (error) {\n      setServicesResult({\n        success: false,\n        message: `Test failed: ${error}`,\n      });\n    }\n    setTesting(false);\n  };\n\n  const runAllTests = async () => {\n    await runConnectionTest();\n    await runServicesTest();\n  };\n\n  return (\n    <div className=\"p-6 bg-white rounded-lg shadow-md\">\n      <h2 className=\"text-xl font-bold mb-4\">Firebase Connection Test</h2>\n      \n      <div className=\"space-y-4\">\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={runConnectionTest}\n            disabled={testing}\n            className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50\"\n          >\n            Test Connection\n          </button>\n          <button\n            onClick={runServicesTest}\n            disabled={testing}\n            className=\"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50\"\n          >\n            Test Services Query\n          </button>\n          <button\n            onClick={runAllTests}\n            disabled={testing}\n            className=\"px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50\"\n          >\n            Run All Tests\n          </button>\n        </div>\n\n        {testing && (\n          <div className=\"text-blue-600\">\n            🔄 Running tests...\n          </div>\n        )}\n\n        {connectionResult && (\n          <div className={`p-4 rounded ${connectionResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>\n            <h3 className=\"font-semibold\">Connection Test Result:</h3>\n            <p>{connectionResult.message}</p>\n            {connectionResult.details && (\n              <pre className=\"mt-2 text-sm\">\n                {JSON.stringify(connectionResult.details, null, 2)}\n              </pre>\n            )}\n          </div>\n        )}\n\n        {servicesResult && (\n          <div className={`p-4 rounded ${servicesResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>\n            <h3 className=\"font-semibold\">Services Query Test Result:</h3>\n            <p>{servicesResult.message}</p>\n            {servicesResult.servicesCount !== undefined && (\n              <p className=\"mt-1\">Services found: {servicesResult.servicesCount}</p>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default FirebaseConnectionTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,sBAAsB,EAAEC,iBAAiB,QAAQ,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS/F,MAAMC,sBAAgC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7C,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGR,QAAQ,CAAoB,IAAI,CAAC;EACjF,MAAM,CAACS,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAoB,IAAI,CAAC;EAC7E,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMa,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAME,MAAM,GAAG,MAAMb,sBAAsB,CAAC,CAAC;MAC7CO,mBAAmB,CAACM,MAAM,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdP,mBAAmB,CAAC;QAClBQ,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,gBAAgBF,KAAK;MAChC,CAAC,CAAC;IACJ;IACAH,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMM,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCN,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAME,MAAM,GAAG,MAAMZ,iBAAiB,CAAC,CAAC;MACxCQ,iBAAiB,CAACI,MAAM,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,iBAAiB,CAAC;QAChBM,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,gBAAgBF,KAAK;MAChC,CAAC,CAAC;IACJ;IACAH,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMO,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,MAAMN,iBAAiB,CAAC,CAAC;IACzB,MAAMK,eAAe,CAAC,CAAC;EACzB,CAAC;EAED,oBACEd,OAAA;IAAKgB,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAChDjB,OAAA;MAAIgB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEpErB,OAAA;MAAKgB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBjB,OAAA;QAAKgB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BjB,OAAA;UACEsB,OAAO,EAAEb,iBAAkB;UAC3Bc,QAAQ,EAAEhB,OAAQ;UAClBS,SAAS,EAAC,gFAAgF;UAAAC,QAAA,EAC3F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrB,OAAA;UACEsB,OAAO,EAAER,eAAgB;UACzBS,QAAQ,EAAEhB,OAAQ;UAClBS,SAAS,EAAC,kFAAkF;UAAAC,QAAA,EAC7F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrB,OAAA;UACEsB,OAAO,EAAEP,WAAY;UACrBQ,QAAQ,EAAEhB,OAAQ;UAClBS,SAAS,EAAC,oFAAoF;UAAAC,QAAA,EAC/F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELd,OAAO,iBACNP,OAAA;QAAKgB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAE/B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,EAEAlB,gBAAgB,iBACfH,OAAA;QAAKgB,SAAS,EAAE,eAAeb,gBAAgB,CAACS,OAAO,GAAG,6BAA6B,GAAG,yBAAyB,EAAG;QAAAK,QAAA,gBACpHjB,OAAA;UAAIgB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1DrB,OAAA;UAAAiB,QAAA,EAAId,gBAAgB,CAACU;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAChClB,gBAAgB,CAACqB,OAAO,iBACvBxB,OAAA;UAAKgB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BQ,IAAI,CAACC,SAAS,CAACvB,gBAAgB,CAACqB,OAAO,EAAE,IAAI,EAAE,CAAC;QAAC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAEAhB,cAAc,iBACbL,OAAA;QAAKgB,SAAS,EAAE,eAAeX,cAAc,CAACO,OAAO,GAAG,6BAA6B,GAAG,yBAAyB,EAAG;QAAAK,QAAA,gBAClHjB,OAAA;UAAIgB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DrB,OAAA;UAAAiB,QAAA,EAAIZ,cAAc,CAACQ;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC9BhB,cAAc,CAACsB,aAAa,KAAKC,SAAS,iBACzC5B,OAAA;UAAGgB,SAAS,EAAC,MAAM;UAAAC,QAAA,GAAC,kBAAgB,EAACZ,cAAc,CAACsB,aAAa;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CACtE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnB,EAAA,CAjGID,sBAAgC;AAAA4B,EAAA,GAAhC5B,sBAAgC;AAmGtC,eAAeA,sBAAsB;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
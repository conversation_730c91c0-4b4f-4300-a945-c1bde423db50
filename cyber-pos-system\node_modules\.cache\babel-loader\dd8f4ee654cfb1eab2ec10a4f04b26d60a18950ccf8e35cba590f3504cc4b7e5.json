{"ast": null, "code": "import { collection, getDocs, query, limit } from 'firebase/firestore';\nimport { db } from '../config/firebase';\n\n/**\n * Test Firebase Firestore connection and basic operations\n */\nexport const testFirebaseConnection = async () => {\n  try {\n    console.log('🔍 Testing Firebase connection...');\n\n    // Test 1: Basic connection by trying to read from a collection\n    const testQuery = query(collection(db, 'services'), limit(1));\n    const snapshot = await getDocs(testQuery);\n    console.log('✅ Firebase connection successful');\n    console.log(`📊 Found ${snapshot.size} documents in services collection`);\n    return {\n      success: true,\n      message: `Firebase connection successful. Found ${snapshot.size} services.`,\n      details: {\n        documentsFound: snapshot.size,\n        connectionTime: new Date().toISOString()\n      }\n    };\n  } catch (error) {\n    console.error('❌ Firebase connection failed:', error);\n    let message = 'Firebase connection failed';\n    if (error.code === 'failed-precondition' && error.message.includes('index')) {\n      message = 'Database index is being created. Please wait a few minutes.';\n    } else if (error.code === 'permission-denied') {\n      message = 'Permission denied. Please check Firebase security rules.';\n    } else if (error.code === 'unavailable') {\n      message = 'Firebase service is temporarily unavailable.';\n    } else {\n      message = `Firebase error: ${error.message}`;\n    }\n    return {\n      success: false,\n      message,\n      details: {\n        errorCode: error.code,\n        errorMessage: error.message,\n        timestamp: new Date().toISOString()\n      }\n    };\n  }\n};\n\n/**\n * Test services query specifically\n */\nexport const testServicesQuery = async () => {\n  try {\n    console.log('🔍 Testing services query...');\n\n    // Test the exact query that was failing\n    const servicesQuery = query(collection(db, 'services'),\n    // Note: We'll test without orderBy first to see if the collection exists\n    limit(10));\n    const snapshot = await getDocs(servicesQuery);\n    console.log(`✅ Services query successful - found ${snapshot.size} services`);\n    return {\n      success: true,\n      message: `Services query successful. Found ${snapshot.size} services.`,\n      servicesCount: snapshot.size\n    };\n  } catch (error) {\n    console.error('❌ Services query failed:', error);\n    return {\n      success: false,\n      message: `Services query failed: ${error.message}`,\n      servicesCount: 0\n    };\n  }\n};", "map": {"version": 3, "names": ["collection", "getDocs", "query", "limit", "db", "testFirebaseConnection", "console", "log", "testQuery", "snapshot", "size", "success", "message", "details", "documentsFound", "connectionTime", "Date", "toISOString", "error", "code", "includes", "errorCode", "errorMessage", "timestamp", "testServicesQuery", "servicesQuery", "servicesCount"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/utils/firebaseConnectionTest.ts"], "sourcesContent": ["import { collection, getDocs, query, limit } from 'firebase/firestore';\nimport { db } from '../config/firebase';\n\n/**\n * Test Firebase Firestore connection and basic operations\n */\nexport const testFirebaseConnection = async (): Promise<{\n  success: boolean;\n  message: string;\n  details?: any;\n}> => {\n  try {\n    console.log('🔍 Testing Firebase connection...');\n    \n    // Test 1: Basic connection by trying to read from a collection\n    const testQuery = query(collection(db, 'services'), limit(1));\n    const snapshot = await getDocs(testQuery);\n    \n    console.log('✅ Firebase connection successful');\n    console.log(`📊 Found ${snapshot.size} documents in services collection`);\n    \n    return {\n      success: true,\n      message: `Firebase connection successful. Found ${snapshot.size} services.`,\n      details: {\n        documentsFound: snapshot.size,\n        connectionTime: new Date().toISOString()\n      }\n    };\n  } catch (error: any) {\n    console.error('❌ Firebase connection failed:', error);\n    \n    let message = 'Firebase connection failed';\n    if (error.code === 'failed-precondition' && error.message.includes('index')) {\n      message = 'Database index is being created. Please wait a few minutes.';\n    } else if (error.code === 'permission-denied') {\n      message = 'Permission denied. Please check Firebase security rules.';\n    } else if (error.code === 'unavailable') {\n      message = 'Firebase service is temporarily unavailable.';\n    } else {\n      message = `Firebase error: ${error.message}`;\n    }\n    \n    return {\n      success: false,\n      message,\n      details: {\n        errorCode: error.code,\n        errorMessage: error.message,\n        timestamp: new Date().toISOString()\n      }\n    };\n  }\n};\n\n/**\n * Test services query specifically\n */\nexport const testServicesQuery = async (): Promise<{\n  success: boolean;\n  message: string;\n  servicesCount?: number;\n}> => {\n  try {\n    console.log('🔍 Testing services query...');\n    \n    // Test the exact query that was failing\n    const servicesQuery = query(\n      collection(db, 'services'),\n      // Note: We'll test without orderBy first to see if the collection exists\n      limit(10)\n    );\n    \n    const snapshot = await getDocs(servicesQuery);\n    \n    console.log(`✅ Services query successful - found ${snapshot.size} services`);\n    \n    return {\n      success: true,\n      message: `Services query successful. Found ${snapshot.size} services.`,\n      servicesCount: snapshot.size\n    };\n  } catch (error: any) {\n    console.error('❌ Services query failed:', error);\n    \n    return {\n      success: false,\n      message: `Services query failed: ${error.message}`,\n      servicesCount: 0\n    };\n  }\n};\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,oBAAoB;AACtE,SAASC,EAAE,QAAQ,oBAAoB;;AAEvC;AACA;AACA;AACA,OAAO,MAAMC,sBAAsB,GAAG,MAAAA,CAAA,KAIhC;EACJ,IAAI;IACFC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;IAEhD;IACA,MAAMC,SAAS,GAAGN,KAAK,CAACF,UAAU,CAACI,EAAE,EAAE,UAAU,CAAC,EAAED,KAAK,CAAC,CAAC,CAAC,CAAC;IAC7D,MAAMM,QAAQ,GAAG,MAAMR,OAAO,CAACO,SAAS,CAAC;IAEzCF,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/CD,OAAO,CAACC,GAAG,CAAC,YAAYE,QAAQ,CAACC,IAAI,mCAAmC,CAAC;IAEzE,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,yCAAyCH,QAAQ,CAACC,IAAI,YAAY;MAC3EG,OAAO,EAAE;QACPC,cAAc,EAAEL,QAAQ,CAACC,IAAI;QAC7BK,cAAc,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACzC;IACF,CAAC;EACH,CAAC,CAAC,OAAOC,KAAU,EAAE;IACnBZ,OAAO,CAACY,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IAErD,IAAIN,OAAO,GAAG,4BAA4B;IAC1C,IAAIM,KAAK,CAACC,IAAI,KAAK,qBAAqB,IAAID,KAAK,CAACN,OAAO,CAACQ,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC3ER,OAAO,GAAG,6DAA6D;IACzE,CAAC,MAAM,IAAIM,KAAK,CAACC,IAAI,KAAK,mBAAmB,EAAE;MAC7CP,OAAO,GAAG,0DAA0D;IACtE,CAAC,MAAM,IAAIM,KAAK,CAACC,IAAI,KAAK,aAAa,EAAE;MACvCP,OAAO,GAAG,8CAA8C;IAC1D,CAAC,MAAM;MACLA,OAAO,GAAG,mBAAmBM,KAAK,CAACN,OAAO,EAAE;IAC9C;IAEA,OAAO;MACLD,OAAO,EAAE,KAAK;MACdC,OAAO;MACPC,OAAO,EAAE;QACPQ,SAAS,EAAEH,KAAK,CAACC,IAAI;QACrBG,YAAY,EAAEJ,KAAK,CAACN,OAAO;QAC3BW,SAAS,EAAE,IAAIP,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC;IACF,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMO,iBAAiB,GAAG,MAAAA,CAAA,KAI3B;EACJ,IAAI;IACFlB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;;IAE3C;IACA,MAAMkB,aAAa,GAAGvB,KAAK,CACzBF,UAAU,CAACI,EAAE,EAAE,UAAU,CAAC;IAC1B;IACAD,KAAK,CAAC,EAAE,CACV,CAAC;IAED,MAAMM,QAAQ,GAAG,MAAMR,OAAO,CAACwB,aAAa,CAAC;IAE7CnB,OAAO,CAACC,GAAG,CAAC,uCAAuCE,QAAQ,CAACC,IAAI,WAAW,CAAC;IAE5E,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,oCAAoCH,QAAQ,CAACC,IAAI,YAAY;MACtEgB,aAAa,EAAEjB,QAAQ,CAACC;IAC1B,CAAC;EACH,CAAC,CAAC,OAAOQ,KAAU,EAAE;IACnBZ,OAAO,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAEhD,OAAO;MACLP,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,0BAA0BM,KAAK,CAACN,OAAO,EAAE;MAClDc,aAAa,EAAE;IACjB,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
[{"E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx": "1", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js": "2", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx": "3", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx": "4", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx": "5", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx": "6", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx": "7", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx": "8", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx": "9", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx": "10", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx": "11", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx": "12", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx": "13", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\offline\\OfflineManager.tsx": "14", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts": "15", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useDashboard.ts": "16", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts": "17", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts": "18", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useReports.ts": "19", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts": "20", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts": "21", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\testDataSeeder.ts": "22", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx": "23", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx": "24", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx": "25", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx": "26", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx": "27", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx": "28", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx": "29", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx": "30", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx": "31", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx": "32", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\InventoryAnalytics.tsx": "33", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx": "34", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useTransactions.ts": "35", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\receiptGenerator.ts": "36", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts": "37", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx": "38", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\BulkOperations.tsx": "39", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\excelImport.ts": "40"}, {"size": 550, "mtime": 1751002257124, "results": "41", "hashOfConfig": "42"}, {"size": 362, "mtime": 1751001516282, "results": "43", "hashOfConfig": "42"}, {"size": 2315, "mtime": 1751014584116, "results": "44", "hashOfConfig": "42"}, {"size": 5912, "mtime": 1751002889593, "results": "45", "hashOfConfig": "42"}, {"size": 9398, "mtime": 1751013933072, "results": "46", "hashOfConfig": "42"}, {"size": 7363, "mtime": 1751020900684, "results": "47", "hashOfConfig": "42"}, {"size": 17602, "mtime": 1751028355487, "results": "48", "hashOfConfig": "42"}, {"size": 8045, "mtime": 1751003816943, "results": "49", "hashOfConfig": "42"}, {"size": 13480, "mtime": 1751003649429, "results": "50", "hashOfConfig": "42"}, {"size": 26580, "mtime": 1751008212115, "results": "51", "hashOfConfig": "42"}, {"size": 8562, "mtime": 1751007249570, "results": "52", "hashOfConfig": "42"}, {"size": 6483, "mtime": 1751001825178, "results": "53", "hashOfConfig": "42"}, {"size": 665, "mtime": 1751001779724, "results": "54", "hashOfConfig": "42"}, {"size": 10403, "mtime": 1751017441838, "results": "55", "hashOfConfig": "42"}, {"size": 3887, "mtime": 1751019446562, "results": "56", "hashOfConfig": "42"}, {"size": 9454, "mtime": 1751013836375, "results": "57", "hashOfConfig": "42"}, {"size": 6079, "mtime": 1751003779872, "results": "58", "hashOfConfig": "42"}, {"size": 4406, "mtime": 1751003274192, "results": "59", "hashOfConfig": "42"}, {"size": 10667, "mtime": 1751006877684, "results": "60", "hashOfConfig": "42"}, {"size": 5107, "mtime": 1751003753867, "results": "61", "hashOfConfig": "42"}, {"size": 9579, "mtime": 1751020844856, "results": "62", "hashOfConfig": "42"}, {"size": 7956, "mtime": 1751007184267, "results": "63", "hashOfConfig": "42"}, {"size": 11510, "mtime": 1751009626258, "results": "64", "hashOfConfig": "42"}, {"size": 12558, "mtime": 1751004256417, "results": "65", "hashOfConfig": "42"}, {"size": 10775, "mtime": 1751004342324, "results": "66", "hashOfConfig": "42"}, {"size": 12479, "mtime": 1751004391960, "results": "67", "hashOfConfig": "42"}, {"size": 15195, "mtime": 1751005588991, "results": "68", "hashOfConfig": "42"}, {"size": 13401, "mtime": 1751003915007, "results": "69", "hashOfConfig": "42"}, {"size": 10910, "mtime": 1751003957303, "results": "70", "hashOfConfig": "42"}, {"size": 3964, "mtime": 1751003442458, "results": "71", "hashOfConfig": "42"}, {"size": 8498, "mtime": 1751003577995, "results": "72", "hashOfConfig": "42"}, {"size": 9054, "mtime": 1751003411814, "results": "73", "hashOfConfig": "42"}, {"size": 11145, "mtime": 1751009654679, "results": "74", "hashOfConfig": "42"}, {"size": 12716, "mtime": 1751002934527, "results": "75", "hashOfConfig": "42"}, {"size": 5054, "mtime": 1751005576405, "results": "76", "hashOfConfig": "42"}, {"size": 7154, "mtime": 1751017196047, "results": "77", "hashOfConfig": "42"}, {"size": 5162, "mtime": 1751003518513, "results": "78", "hashOfConfig": "42"}, {"size": 11914, "mtime": 1751004034012, "results": "79", "hashOfConfig": "42"}, {"size": 22868, "mtime": 1751028366405, "results": "80", "hashOfConfig": "42"}, {"size": 13824, "mtime": 1751026107868, "results": "81", "hashOfConfig": "42"}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "do8dzb", {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx", ["202"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx", ["203"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx", ["204", "205", "206", "207", "208", "209", "210", "211"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx", ["212", "213", "214", "215"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx", ["216", "217", "218"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\offline\\OfflineManager.tsx", ["219", "220", "221"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts", ["222"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useDashboard.ts", ["223", "224"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts", ["225", "226"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts", ["227", "228"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useReports.ts", ["229", "230", "231", "232", "233", "234"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts", ["235", "236"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts", ["237", "238", "239"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\testDataSeeder.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx", ["240"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx", ["241"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\InventoryAnalytics.tsx", ["242", "243"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx", ["244", "245"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useTransactions.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\receiptGenerator.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx", ["246"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\BulkOperations.tsx", ["247"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\excelImport.ts", ["248"], [], {"ruleId": "249", "severity": 1, "message": "250", "line": 17, "column": 7, "nodeType": "251", "messageId": "252", "endLine": 17, "endColumn": 62}, {"ruleId": "249", "severity": 1, "message": "253", "line": 16, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 16, "endColumn": 8}, {"ruleId": "249", "severity": 1, "message": "254", "line": 10, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 10, "endColumn": 7}, {"ruleId": "249", "severity": 1, "message": "255", "line": 11, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 11, "endColumn": 13}, {"ruleId": "249", "severity": 1, "message": "256", "line": 12, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 12, "endColumn": 10}, {"ruleId": "249", "severity": 1, "message": "257", "line": 13, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 13, "endColumn": 9}, {"ruleId": "249", "severity": 1, "message": "258", "line": 14, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 14, "endColumn": 7}, {"ruleId": "249", "severity": 1, "message": "259", "line": 15, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 15, "endColumn": 8}, {"ruleId": "249", "severity": 1, "message": "260", "line": 21, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 21, "endColumn": 17}, {"ruleId": "249", "severity": 1, "message": "261", "line": 21, "column": 19, "nodeType": "251", "messageId": "252", "endLine": 21, "endColumn": 26}, {"ruleId": "249", "severity": 1, "message": "262", "line": 9, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 9, "endColumn": 13}, {"ruleId": "249", "severity": 1, "message": "263", "line": 10, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 10, "endColumn": 6}, {"ruleId": "249", "severity": 1, "message": "264", "line": 14, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 14, "endColumn": 4}, {"ruleId": "249", "severity": 1, "message": "265", "line": 15, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 15, "endColumn": 7}, {"ruleId": "249", "severity": 1, "message": "266", "line": 11, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 11, "endColumn": 9}, {"ruleId": "249", "severity": 1, "message": "267", "line": 26, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 26, "endColumn": 13}, {"ruleId": "268", "severity": 1, "message": "269", "line": 57, "column": 6, "nodeType": "270", "endLine": 57, "endColumn": 22, "suggestions": "271"}, {"ruleId": "249", "severity": 1, "message": "272", "line": 6, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 6, "endColumn": 14}, {"ruleId": "249", "severity": 1, "message": "273", "line": 7, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 7, "endColumn": 14}, {"ruleId": "268", "severity": 1, "message": "274", "line": 51, "column": 6, "nodeType": "270", "endLine": 51, "endColumn": 27, "suggestions": "275"}, {"ruleId": "249", "severity": 1, "message": "276", "line": 8, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 8, "endColumn": 23}, {"ruleId": "249", "severity": 1, "message": "261", "line": 12, "column": 23, "nodeType": "251", "messageId": "252", "endLine": 12, "endColumn": 30}, {"ruleId": "268", "severity": 1, "message": "277", "line": 300, "column": 6, "nodeType": "270", "endLine": 300, "endColumn": 8, "suggestions": "278"}, {"ruleId": "249", "severity": 1, "message": "279", "line": 5, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 5, "endColumn": 10}, {"ruleId": "249", "severity": 1, "message": "253", "line": 11, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 11, "endColumn": 8}, {"ruleId": "249", "severity": 1, "message": "279", "line": 5, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 5, "endColumn": 10}, {"ruleId": "249", "severity": 1, "message": "253", "line": 11, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 11, "endColumn": 8}, {"ruleId": "249", "severity": 1, "message": "280", "line": 1, "column": 20, "nodeType": "251", "messageId": "252", "endLine": 1, "endColumn": 29}, {"ruleId": "249", "severity": 1, "message": "281", "line": 9, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 9, "endColumn": 13}, {"ruleId": "249", "severity": 1, "message": "282", "line": 10, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 10, "endColumn": 8}, {"ruleId": "249", "severity": 1, "message": "261", "line": 13, "column": 23, "nodeType": "251", "messageId": "252", "endLine": 13, "endColumn": 30}, {"ruleId": "249", "severity": 1, "message": "260", "line": 13, "column": 32, "nodeType": "251", "messageId": "252", "endLine": 13, "endColumn": 39}, {"ruleId": "249", "severity": 1, "message": "254", "line": 13, "column": 41, "nodeType": "251", "messageId": "252", "endLine": 13, "endColumn": 45}, {"ruleId": "249", "severity": 1, "message": "283", "line": 3, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 3, "endColumn": 31}, {"ruleId": "268", "severity": 1, "message": "284", "line": 87, "column": 6, "nodeType": "270", "endLine": 87, "endColumn": 8, "suggestions": "285"}, {"ruleId": "249", "severity": 1, "message": "254", "line": 14, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 14, "endColumn": 14}, {"ruleId": "249", "severity": 1, "message": "260", "line": 14, "column": 16, "nodeType": "251", "messageId": "252", "endLine": 14, "endColumn": 23}, {"ruleId": "249", "severity": 1, "message": "261", "line": 14, "column": 25, "nodeType": "251", "messageId": "252", "endLine": 14, "endColumn": 32}, {"ruleId": "249", "severity": 1, "message": "262", "line": 5, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 5, "endColumn": 13}, {"ruleId": "249", "severity": 1, "message": "263", "line": 2, "column": 25, "nodeType": "251", "messageId": "252", "endLine": 2, "endColumn": 28}, {"ruleId": "249", "severity": 1, "message": "261", "line": 12, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 12, "endColumn": 17}, {"ruleId": "268", "severity": 1, "message": "286", "line": 45, "column": 6, "nodeType": "270", "endLine": 45, "endColumn": 16, "suggestions": "287"}, {"ruleId": "249", "severity": 1, "message": "257", "line": 8, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 8, "endColumn": 9}, {"ruleId": "268", "severity": 1, "message": "288", "line": 34, "column": 6, "nodeType": "270", "endLine": 34, "endColumn": 8, "suggestions": "289"}, {"ruleId": "249", "severity": 1, "message": "290", "line": 9, "column": 3, "nodeType": "251", "messageId": "252", "endLine": 9, "endColumn": 10}, {"ruleId": "249", "severity": 1, "message": "291", "line": 39, "column": 10, "nodeType": "251", "messageId": "252", "endLine": 39, "endColumn": 20}, {"ruleId": "249", "severity": 1, "message": "292", "line": 21, "column": 7, "nodeType": "251", "messageId": "252", "endLine": 21, "endColumn": 23}, "@typescript-eslint/no-unused-vars", "'ProtectedRoute' is assigned a value but never used.", "Identifier", "unusedVar", "'where' is defined but never used.", "'User' is defined but never used.", "'CreditCard' is defined but never used.", "'Receipt' is defined but never used.", "'Trash2' is defined but never used.", "'Plus' is defined but never used.", "'Minus' is defined but never used.", "'Service' is defined but never used.", "'Product' is defined but never used.", "'DollarSign' is defined but never used.", "'Tag' is defined but never used.", "'X' is defined but never used.", "'Save' is defined but never used.", "'Filter' is defined but never used.", "'Bar' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadReport'. Either include it or remove the dependency array.", "ArrayExpression", ["293"], "'AlertCircle' is defined but never used.", "'CheckCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'syncOfflineData'. Either include it or remove the dependency array.", ["294"], "'persistentLocalCache' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", ["295"], "'getDocs' is defined but never used.", "'useEffect' is defined but never used.", "'startAfter' is defined but never used.", "'limit' is defined but never used.", "'calculateServicePrice' is defined but never used.", "React Hook useCallback has a missing dependency: 'removeFromCart'. Either include it or remove the dependency array.", ["296"], "React Hook useEffect has missing dependencies: 'calculateMetrics' and 'generateAlerts'. Either include them or remove the dependency array.", ["297"], "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["298"], "'Printer' is defined but never used.", "'importFile' is assigned a value but never used.", "'OPTIONAL_COLUMNS' is assigned a value but never used.", {"desc": "299", "fix": "300"}, {"desc": "301", "fix": "302"}, {"desc": "303", "fix": "304"}, {"desc": "305", "fix": "306"}, {"desc": "307", "fix": "308"}, {"desc": "309", "fix": "310"}, "Update the dependencies array to be: [loadReport, selectedPeriod]", {"range": "311", "text": "312"}, "Update the dependencies array to be: [offlineQueue.length, syncOfflineData]", {"range": "313", "text": "314"}, "Update the dependencies array to be: [loadDashboardData]", {"range": "315", "text": "316"}, "Update the dependencies array to be: [removeFromCart]", {"range": "317", "text": "318"}, "Update the dependencies array to be: [calculateMetrics, generateAlerts, products]", {"range": "319", "text": "320"}, "Update the dependencies array to be: [loadUsers]", {"range": "321", "text": "322"}, [1381, 1397], "[load<PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON>]", [1357, 1378], "[offlineQueue.length, syncOfflineData]", [9246, 9248], "[loadDashboardData]", [2571, 2573], "[remove<PERSON><PERSON><PERSON><PERSON>]", [1163, 1173], "[calculateMetrics, generateAlerts, products]", [865, 867], "[loadUsers]"]
{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { collection, doc, getDoc, addDoc, updateDoc, deleteDoc, query, orderBy, onSnapshot, serverTimestamp } from 'firebase/firestore';\nimport { db } from '../config/firebase';\nexport const useServices = () => {\n  _s();\n  const [services, setServices] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Real-time listener for services\n  useEffect(() => {\n    let unsubscribe = null;\n    const setupListener = async () => {\n      try {\n        const servicesQuery = query(collection(db, 'services'), orderBy('category'), orderBy('name'));\n        unsubscribe = onSnapshot(servicesQuery, snapshot => {\n          const servicesData = [];\n          snapshot.forEach(doc => {\n            var _data$createdAt, _data$updatedAt;\n            const data = doc.data();\n            servicesData.push({\n              id: doc.id,\n              name: data.name || '',\n              description: data.description || '',\n              basePrice: data.basePrice || 0,\n              category: data.category || '',\n              isActive: data.isActive !== false,\n              allowPriceOverride: data.allowPriceOverride !== false,\n              bundledServices: data.bundledServices || [],\n              createdAt: ((_data$createdAt = data.createdAt) === null || _data$createdAt === void 0 ? void 0 : _data$createdAt.toDate()) || new Date(),\n              updatedAt: ((_data$updatedAt = data.updatedAt) === null || _data$updatedAt === void 0 ? void 0 : _data$updatedAt.toDate()) || new Date()\n            });\n          });\n          setServices(servicesData);\n          setLoading(false);\n          setError(null);\n          console.log(`✅ Successfully loaded ${servicesData.length} services`);\n        }, error => {\n          console.error('Error fetching services:', error);\n\n          // Check if it's an index error\n          if (error.code === 'failed-precondition' && error.message.includes('index')) {\n            setError('Database index is being created. Please wait a few minutes and refresh the page.');\n          } else {\n            setError(`Failed to fetch services: ${error.message}`);\n          }\n          setLoading(false);\n        });\n      } catch (error) {\n        console.error('Error setting up services listener:', error);\n        setError('Failed to initialize services');\n        setLoading(false);\n      }\n    };\n    setupListener();\n    return () => unsubscribe();\n  }, []);\n  const createService = async serviceData => {\n    try {\n      setError(null);\n      await addDoc(collection(db, 'services'), {\n        ...serviceData,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp()\n      });\n    } catch (error) {\n      console.error('Error creating service:', error);\n      setError('Failed to create service');\n      throw error;\n    }\n  };\n  const updateService = async (serviceId, updates) => {\n    try {\n      setError(null);\n      const {\n        id,\n        createdAt,\n        ...updateData\n      } = updates;\n      await updateDoc(doc(db, 'services', serviceId), {\n        ...updateData,\n        updatedAt: serverTimestamp()\n      });\n    } catch (error) {\n      console.error('Error updating service:', error);\n      setError('Failed to update service');\n      throw error;\n    }\n  };\n  const deleteService = async serviceId => {\n    try {\n      setError(null);\n      await deleteDoc(doc(db, 'services', serviceId));\n    } catch (error) {\n      console.error('Error deleting service:', error);\n      setError('Failed to delete service');\n      throw error;\n    }\n  };\n  const getServiceById = async serviceId => {\n    try {\n      const serviceDoc = await getDoc(doc(db, 'services', serviceId));\n      if (serviceDoc.exists()) {\n        var _data$createdAt2, _data$updatedAt2;\n        const data = serviceDoc.data();\n        return {\n          id: serviceDoc.id,\n          name: data.name || '',\n          description: data.description || '',\n          basePrice: data.basePrice || 0,\n          category: data.category || '',\n          isActive: data.isActive !== false,\n          allowPriceOverride: data.allowPriceOverride !== false,\n          bundledServices: data.bundledServices || [],\n          createdAt: ((_data$createdAt2 = data.createdAt) === null || _data$createdAt2 === void 0 ? void 0 : _data$createdAt2.toDate()) || new Date(),\n          updatedAt: ((_data$updatedAt2 = data.updatedAt) === null || _data$updatedAt2 === void 0 ? void 0 : _data$updatedAt2.toDate()) || new Date()\n        };\n      }\n      return null;\n    } catch (error) {\n      console.error('Error fetching service:', error);\n      throw error;\n    }\n  };\n  const getServicesByCategory = category => {\n    return services.filter(service => service.category === category && service.isActive);\n  };\n  const getActiveServices = () => {\n    return services.filter(service => service.isActive);\n  };\n  const getServiceCategories = () => {\n    const categories = [...new Set(services.map(service => service.category))];\n    return categories.sort();\n  };\n  return {\n    services,\n    loading,\n    error,\n    createService,\n    updateService,\n    deleteService,\n    getServiceById,\n    getServicesByCategory,\n    getActiveServices,\n    getServiceCategories\n  };\n};\n_s(useServices, \"l7D1/i509iW4jLhBJLLaY6cVuzw=\");", "map": {"version": 3, "names": ["useState", "useEffect", "collection", "doc", "getDoc", "addDoc", "updateDoc", "deleteDoc", "query", "orderBy", "onSnapshot", "serverTimestamp", "db", "useServices", "_s", "services", "setServices", "loading", "setLoading", "error", "setError", "unsubscribe", "setupListener", "servicesQuery", "snapshot", "servicesData", "for<PERSON>ach", "_data$createdAt", "_data$updatedAt", "data", "push", "id", "name", "description", "basePrice", "category", "isActive", "allowPriceOverride", "bundledServices", "createdAt", "toDate", "Date", "updatedAt", "console", "log", "length", "code", "message", "includes", "createService", "serviceData", "updateService", "serviceId", "updates", "updateData", "deleteService", "getServiceById", "serviceDoc", "exists", "_data$createdAt2", "_data$updatedAt2", "getServicesByCategory", "filter", "service", "getActiveServices", "getServiceCategories", "categories", "Set", "map", "sort"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/hooks/useServices.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport {\n  collection,\n  doc,\n  getDocs,\n  getDoc,\n  addDoc,\n  updateDoc,\n  deleteDoc,\n  query,\n  where,\n  orderBy,\n  onSnapshot,\n  serverTimestamp\n} from 'firebase/firestore';\nimport { db } from '../config/firebase';\nimport { Service } from '../types';\n\nexport const useServices = () => {\n  const [services, setServices] = useState<Service[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Real-time listener for services\n  useEffect(() => {\n    let unsubscribe: (() => void) | null = null;\n\n    const setupListener = async () => {\n      try {\n        const servicesQuery = query(\n          collection(db, 'services'),\n          orderBy('category'),\n          orderBy('name')\n        );\n\n        unsubscribe = onSnapshot(\n          servicesQuery,\n          (snapshot) => {\n            const servicesData: Service[] = [];\n            snapshot.forEach((doc) => {\n              const data = doc.data();\n              servicesData.push({\n                id: doc.id,\n                name: data.name || '',\n                description: data.description || '',\n                basePrice: data.basePrice || 0,\n                category: data.category || '',\n                isActive: data.isActive !== false,\n                allowPriceOverride: data.allowPriceOverride !== false,\n                bundledServices: data.bundledServices || [],\n                createdAt: data.createdAt?.toDate() || new Date(),\n                updatedAt: data.updatedAt?.toDate() || new Date(),\n              });\n            });\n            setServices(servicesData);\n            setLoading(false);\n            setError(null);\n            console.log(`✅ Successfully loaded ${servicesData.length} services`);\n          },\n          (error) => {\n            console.error('Error fetching services:', error);\n\n            // Check if it's an index error\n            if (error.code === 'failed-precondition' && error.message.includes('index')) {\n              setError('Database index is being created. Please wait a few minutes and refresh the page.');\n            } else {\n              setError(`Failed to fetch services: ${error.message}`);\n            }\n            setLoading(false);\n          }\n        );\n      } catch (error) {\n        console.error('Error setting up services listener:', error);\n        setError('Failed to initialize services');\n        setLoading(false);\n      }\n    };\n\n    setupListener();\n\n    return () => unsubscribe();\n  }, []);\n\n  const createService = async (serviceData: Omit<Service, 'id' | 'createdAt' | 'updatedAt'>) => {\n    try {\n      setError(null);\n      await addDoc(collection(db, 'services'), {\n        ...serviceData,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp(),\n      });\n    } catch (error) {\n      console.error('Error creating service:', error);\n      setError('Failed to create service');\n      throw error;\n    }\n  };\n\n  const updateService = async (serviceId: string, updates: Partial<Service>) => {\n    try {\n      setError(null);\n      const { id, createdAt, ...updateData } = updates;\n      await updateDoc(doc(db, 'services', serviceId), {\n        ...updateData,\n        updatedAt: serverTimestamp(),\n      });\n    } catch (error) {\n      console.error('Error updating service:', error);\n      setError('Failed to update service');\n      throw error;\n    }\n  };\n\n  const deleteService = async (serviceId: string) => {\n    try {\n      setError(null);\n      await deleteDoc(doc(db, 'services', serviceId));\n    } catch (error) {\n      console.error('Error deleting service:', error);\n      setError('Failed to delete service');\n      throw error;\n    }\n  };\n\n  const getServiceById = async (serviceId: string): Promise<Service | null> => {\n    try {\n      const serviceDoc = await getDoc(doc(db, 'services', serviceId));\n      if (serviceDoc.exists()) {\n        const data = serviceDoc.data();\n        return {\n          id: serviceDoc.id,\n          name: data.name || '',\n          description: data.description || '',\n          basePrice: data.basePrice || 0,\n          category: data.category || '',\n          isActive: data.isActive !== false,\n          allowPriceOverride: data.allowPriceOverride !== false,\n          bundledServices: data.bundledServices || [],\n          createdAt: data.createdAt?.toDate() || new Date(),\n          updatedAt: data.updatedAt?.toDate() || new Date(),\n        };\n      }\n      return null;\n    } catch (error) {\n      console.error('Error fetching service:', error);\n      throw error;\n    }\n  };\n\n  const getServicesByCategory = (category: string) => {\n    return services.filter(service => service.category === category && service.isActive);\n  };\n\n  const getActiveServices = () => {\n    return services.filter(service => service.isActive);\n  };\n\n  const getServiceCategories = () => {\n    const categories = [...new Set(services.map(service => service.category))];\n    return categories.sort();\n  };\n\n  return {\n    services,\n    loading,\n    error,\n    createService,\n    updateService,\n    deleteService,\n    getServiceById,\n    getServicesByCategory,\n    getActiveServices,\n    getServiceCategories,\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SACEC,UAAU,EACVC,GAAG,EAEHC,MAAM,EACNC,MAAM,EACNC,SAAS,EACTC,SAAS,EACTC,KAAK,EAELC,OAAO,EACPC,UAAU,EACVC,eAAe,QACV,oBAAoB;AAC3B,SAASC,EAAE,QAAQ,oBAAoB;AAGvC,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIoB,WAAgC,GAAG,IAAI;IAE3C,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMC,aAAa,GAAGf,KAAK,CACzBN,UAAU,CAACU,EAAE,EAAE,UAAU,CAAC,EAC1BH,OAAO,CAAC,UAAU,CAAC,EACnBA,OAAO,CAAC,MAAM,CAChB,CAAC;QAEDY,WAAW,GAAGX,UAAU,CACtBa,aAAa,EACZC,QAAQ,IAAK;UACZ,MAAMC,YAAuB,GAAG,EAAE;UAClCD,QAAQ,CAACE,OAAO,CAAEvB,GAAG,IAAK;YAAA,IAAAwB,eAAA,EAAAC,eAAA;YACxB,MAAMC,IAAI,GAAG1B,GAAG,CAAC0B,IAAI,CAAC,CAAC;YACvBJ,YAAY,CAACK,IAAI,CAAC;cAChBC,EAAE,EAAE5B,GAAG,CAAC4B,EAAE;cACVC,IAAI,EAAEH,IAAI,CAACG,IAAI,IAAI,EAAE;cACrBC,WAAW,EAAEJ,IAAI,CAACI,WAAW,IAAI,EAAE;cACnCC,SAAS,EAAEL,IAAI,CAACK,SAAS,IAAI,CAAC;cAC9BC,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAI,EAAE;cAC7BC,QAAQ,EAAEP,IAAI,CAACO,QAAQ,KAAK,KAAK;cACjCC,kBAAkB,EAAER,IAAI,CAACQ,kBAAkB,KAAK,KAAK;cACrDC,eAAe,EAAET,IAAI,CAACS,eAAe,IAAI,EAAE;cAC3CC,SAAS,EAAE,EAAAZ,eAAA,GAAAE,IAAI,CAACU,SAAS,cAAAZ,eAAA,uBAAdA,eAAA,CAAgBa,MAAM,CAAC,CAAC,KAAI,IAAIC,IAAI,CAAC,CAAC;cACjDC,SAAS,EAAE,EAAAd,eAAA,GAAAC,IAAI,CAACa,SAAS,cAAAd,eAAA,uBAAdA,eAAA,CAAgBY,MAAM,CAAC,CAAC,KAAI,IAAIC,IAAI,CAAC;YAClD,CAAC,CAAC;UACJ,CAAC,CAAC;UACFzB,WAAW,CAACS,YAAY,CAAC;UACzBP,UAAU,CAAC,KAAK,CAAC;UACjBE,QAAQ,CAAC,IAAI,CAAC;UACduB,OAAO,CAACC,GAAG,CAAC,yBAAyBnB,YAAY,CAACoB,MAAM,WAAW,CAAC;QACtE,CAAC,EACA1B,KAAK,IAAK;UACTwB,OAAO,CAACxB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;;UAEhD;UACA,IAAIA,KAAK,CAAC2B,IAAI,KAAK,qBAAqB,IAAI3B,KAAK,CAAC4B,OAAO,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC3E5B,QAAQ,CAAC,kFAAkF,CAAC;UAC9F,CAAC,MAAM;YACLA,QAAQ,CAAC,6BAA6BD,KAAK,CAAC4B,OAAO,EAAE,CAAC;UACxD;UACA7B,UAAU,CAAC,KAAK,CAAC;QACnB,CACF,CAAC;MACH,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdwB,OAAO,CAACxB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3DC,QAAQ,CAAC,+BAA+B,CAAC;QACzCF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDI,aAAa,CAAC,CAAC;IAEf,OAAO,MAAMD,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4B,aAAa,GAAG,MAAOC,WAA4D,IAAK;IAC5F,IAAI;MACF9B,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMf,MAAM,CAACH,UAAU,CAACU,EAAE,EAAE,UAAU,CAAC,EAAE;QACvC,GAAGsC,WAAW;QACdX,SAAS,EAAE5B,eAAe,CAAC,CAAC;QAC5B+B,SAAS,EAAE/B,eAAe,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdwB,OAAO,CAACxB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;MACpC,MAAMD,KAAK;IACb;EACF,CAAC;EAED,MAAMgC,aAAa,GAAG,MAAAA,CAAOC,SAAiB,EAAEC,OAAyB,KAAK;IAC5E,IAAI;MACFjC,QAAQ,CAAC,IAAI,CAAC;MACd,MAAM;QAAEW,EAAE;QAAEQ,SAAS;QAAE,GAAGe;MAAW,CAAC,GAAGD,OAAO;MAChD,MAAM/C,SAAS,CAACH,GAAG,CAACS,EAAE,EAAE,UAAU,EAAEwC,SAAS,CAAC,EAAE;QAC9C,GAAGE,UAAU;QACbZ,SAAS,EAAE/B,eAAe,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdwB,OAAO,CAACxB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;MACpC,MAAMD,KAAK;IACb;EACF,CAAC;EAED,MAAMoC,aAAa,GAAG,MAAOH,SAAiB,IAAK;IACjD,IAAI;MACFhC,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMb,SAAS,CAACJ,GAAG,CAACS,EAAE,EAAE,UAAU,EAAEwC,SAAS,CAAC,CAAC;IACjD,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdwB,OAAO,CAACxB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;MACpC,MAAMD,KAAK;IACb;EACF,CAAC;EAED,MAAMqC,cAAc,GAAG,MAAOJ,SAAiB,IAA8B;IAC3E,IAAI;MACF,MAAMK,UAAU,GAAG,MAAMrD,MAAM,CAACD,GAAG,CAACS,EAAE,EAAE,UAAU,EAAEwC,SAAS,CAAC,CAAC;MAC/D,IAAIK,UAAU,CAACC,MAAM,CAAC,CAAC,EAAE;QAAA,IAAAC,gBAAA,EAAAC,gBAAA;QACvB,MAAM/B,IAAI,GAAG4B,UAAU,CAAC5B,IAAI,CAAC,CAAC;QAC9B,OAAO;UACLE,EAAE,EAAE0B,UAAU,CAAC1B,EAAE;UACjBC,IAAI,EAAEH,IAAI,CAACG,IAAI,IAAI,EAAE;UACrBC,WAAW,EAAEJ,IAAI,CAACI,WAAW,IAAI,EAAE;UACnCC,SAAS,EAAEL,IAAI,CAACK,SAAS,IAAI,CAAC;UAC9BC,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAI,EAAE;UAC7BC,QAAQ,EAAEP,IAAI,CAACO,QAAQ,KAAK,KAAK;UACjCC,kBAAkB,EAAER,IAAI,CAACQ,kBAAkB,KAAK,KAAK;UACrDC,eAAe,EAAET,IAAI,CAACS,eAAe,IAAI,EAAE;UAC3CC,SAAS,EAAE,EAAAoB,gBAAA,GAAA9B,IAAI,CAACU,SAAS,cAAAoB,gBAAA,uBAAdA,gBAAA,CAAgBnB,MAAM,CAAC,CAAC,KAAI,IAAIC,IAAI,CAAC,CAAC;UACjDC,SAAS,EAAE,EAAAkB,gBAAA,GAAA/B,IAAI,CAACa,SAAS,cAAAkB,gBAAA,uBAAdA,gBAAA,CAAgBpB,MAAM,CAAC,CAAC,KAAI,IAAIC,IAAI,CAAC;QAClD,CAAC;MACH;MACA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdwB,OAAO,CAACxB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAM0C,qBAAqB,GAAI1B,QAAgB,IAAK;IAClD,OAAOpB,QAAQ,CAAC+C,MAAM,CAACC,OAAO,IAAIA,OAAO,CAAC5B,QAAQ,KAAKA,QAAQ,IAAI4B,OAAO,CAAC3B,QAAQ,CAAC;EACtF,CAAC;EAED,MAAM4B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAOjD,QAAQ,CAAC+C,MAAM,CAACC,OAAO,IAAIA,OAAO,CAAC3B,QAAQ,CAAC;EACrD,CAAC;EAED,MAAM6B,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,UAAU,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACpD,QAAQ,CAACqD,GAAG,CAACL,OAAO,IAAIA,OAAO,CAAC5B,QAAQ,CAAC,CAAC,CAAC;IAC1E,OAAO+B,UAAU,CAACG,IAAI,CAAC,CAAC;EAC1B,CAAC;EAED,OAAO;IACLtD,QAAQ;IACRE,OAAO;IACPE,KAAK;IACL8B,aAAa;IACbE,aAAa;IACbI,aAAa;IACbC,cAAc;IACdK,qBAAqB;IACrBG,iBAAiB;IACjBC;EACF,CAAC;AACH,CAAC;AAACnD,EAAA,CA5JWD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
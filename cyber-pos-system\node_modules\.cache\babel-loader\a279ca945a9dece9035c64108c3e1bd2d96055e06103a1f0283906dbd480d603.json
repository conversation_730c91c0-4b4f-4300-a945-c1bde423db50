{"ast": null, "code": "import * as XLSX from 'xlsx';\n// Required columns for product import\nconst REQUIRED_COLUMNS = ['Name', 'Category', 'Price', 'Stock Quantity'];\n\n// Optional columns with their default values\nconst OPTIONAL_COLUMNS = {\n  'Description': '',\n  'Reorder Level': 10,\n  'Has Expiry': false,\n  'Expiry Date': null,\n  'Is Active': true\n};\n\n// Column mapping for different naming conventions\nconst COLUMN_MAPPINGS = {\n  'product name': 'Name',\n  'product_name': 'Name',\n  'productname': 'Name',\n  'item name': 'Name',\n  'item_name': 'Name',\n  'itemname': 'Name',\n  'title': 'Name',\n  'product category': 'Category',\n  'product_category': 'Category',\n  'productcategory': 'Category',\n  'item category': 'Category',\n  'item_category': 'Category',\n  'itemcategory': 'Category',\n  'cat': 'Category',\n  'unit price': 'Price',\n  'unit_price': 'Price',\n  'unitprice': 'Price',\n  'cost': 'Price',\n  'amount': 'Price',\n  'stock': 'Stock Quantity',\n  'quantity': 'Stock Quantity',\n  'qty': 'Stock Quantity',\n  'inventory': 'Stock Quantity',\n  'stock_quantity': 'Stock Quantity',\n  'stockquantity': 'Stock Quantity',\n  'desc': 'Description',\n  'product description': 'Description',\n  'product_description': 'Description',\n  'productdescription': 'Description',\n  'item description': 'Description',\n  'item_description': 'Description',\n  'itemdescription': 'Description',\n  'reorder': 'Reorder Level',\n  'reorder_level': 'Reorder Level',\n  'reorderlevel': 'Reorder Level',\n  'min stock': 'Reorder Level',\n  'min_stock': 'Reorder Level',\n  'minstock': 'Reorder Level',\n  'minimum': 'Reorder Level',\n  'expiry': 'Has Expiry',\n  'has_expiry': 'Has Expiry',\n  'hasexpiry': 'Has Expiry',\n  'expires': 'Has Expiry',\n  'perishable': 'Has Expiry',\n  'expiry_date': 'Expiry Date',\n  'expirydate': 'Expiry Date',\n  'expiration': 'Expiry Date',\n  'expiration_date': 'Expiry Date',\n  'expirationdate': 'Expiry Date',\n  'expires_on': 'Expiry Date',\n  'expireson': 'Expiry Date',\n  'active': 'Is Active',\n  'is_active': 'Is Active',\n  'isactive': 'Is Active',\n  'enabled': 'Is Active',\n  'status': 'Is Active'\n};\n\n/**\n * Normalize column headers to match expected format\n */\nfunction normalizeHeaders(headers) {\n  return headers.map(header => {\n    const normalized = header.toLowerCase().trim();\n    return COLUMN_MAPPINGS[normalized] || header;\n  });\n}\n\n/**\n * Validate that required columns are present\n */\nfunction validateHeaders(headers) {\n  const normalizedHeaders = normalizeHeaders(headers);\n  const errors = [];\n  const warnings = [];\n\n  // Check for required columns\n  const missingRequired = REQUIRED_COLUMNS.filter(required => !normalizedHeaders.includes(required));\n  if (missingRequired.length > 0) {\n    errors.push(`Missing required columns: ${missingRequired.join(', ')}`);\n  }\n\n  // Check for duplicate columns\n  const duplicates = normalizedHeaders.filter((header, index) => normalizedHeaders.indexOf(header) !== index);\n  if (duplicates.length > 0) {\n    warnings.push(`Duplicate columns found: ${duplicates.join(', ')}`);\n  }\n  return {\n    isValid: errors.length === 0,\n    errors,\n    warnings\n  };\n}\n\n/**\n * Parse and validate a single row of product data\n */\nfunction parseProductRow(rowData, rowIndex) {\n  const errors = [];\n  const product = {};\n  try {\n    // Name (required)\n    const name = String(rowData['Name'] || '').trim();\n    if (!name) {\n      errors.push(`Row ${rowIndex}: Name is required`);\n    } else {\n      product.name = name;\n    }\n\n    // Category (required)\n    const category = String(rowData['Category'] || '').trim();\n    if (!category) {\n      errors.push(`Row ${rowIndex}: Category is required`);\n    } else {\n      product.category = category;\n    }\n\n    // Price (required)\n    const priceValue = rowData['Price'];\n    if (priceValue === undefined || priceValue === null || priceValue === '') {\n      errors.push(`Row ${rowIndex}: Price is required`);\n    } else {\n      const price = parseFloat(String(priceValue).replace(/[^\\d.-]/g, ''));\n      if (isNaN(price) || price < 0) {\n        errors.push(`Row ${rowIndex}: Price must be a valid positive number`);\n      } else {\n        product.price = price;\n      }\n    }\n\n    // Stock Quantity (required)\n    const stockValue = rowData['Stock Quantity'];\n    if (stockValue === undefined || stockValue === null || stockValue === '') {\n      errors.push(`Row ${rowIndex}: Stock Quantity is required`);\n    } else {\n      const stockQuantity = parseInt(String(stockValue));\n      if (isNaN(stockQuantity) || stockQuantity < 0) {\n        errors.push(`Row ${rowIndex}: Stock Quantity must be a valid non-negative integer`);\n      } else {\n        product.stockQuantity = stockQuantity;\n      }\n    }\n\n    // Description (optional)\n    const description = String(rowData['Description'] || '').trim();\n    product.description = description;\n\n    // Reorder Level (optional)\n    const reorderValue = rowData['Reorder Level'];\n    if (reorderValue !== undefined && reorderValue !== null && reorderValue !== '') {\n      const reorderLevel = parseInt(String(reorderValue));\n      if (isNaN(reorderLevel) || reorderLevel < 0) {\n        errors.push(`Row ${rowIndex}: Reorder Level must be a valid non-negative integer`);\n      } else {\n        product.reorderLevel = reorderLevel;\n      }\n    } else {\n      product.reorderLevel = 10; // Default value\n    }\n\n    // Has Expiry (optional)\n    const hasExpiryValue = rowData['Has Expiry'];\n    if (hasExpiryValue !== undefined && hasExpiryValue !== null && hasExpiryValue !== '') {\n      const hasExpiryStr = String(hasExpiryValue).toLowerCase().trim();\n      product.hasExpiry = ['true', '1', 'yes', 'y'].includes(hasExpiryStr);\n    } else {\n      product.hasExpiry = false;\n    }\n\n    // Expiry Date (optional)\n    const expiryDateValue = rowData['Expiry Date'];\n    if (expiryDateValue && product.hasExpiry) {\n      try {\n        let expiryDate;\n        if (typeof expiryDateValue === 'number') {\n          // Excel date serial number\n          expiryDate = XLSX.SSF.parse_date_code(expiryDateValue);\n        } else {\n          // String date\n          expiryDate = new Date(String(expiryDateValue));\n        }\n        if (isNaN(expiryDate.getTime())) {\n          errors.push(`Row ${rowIndex}: Invalid expiry date format`);\n        } else {\n          product.expiryDate = expiryDate;\n        }\n      } catch (error) {\n        errors.push(`Row ${rowIndex}: Invalid expiry date format`);\n      }\n    }\n\n    // Is Active (optional)\n    const isActiveValue = rowData['Is Active'];\n    if (isActiveValue !== undefined && isActiveValue !== null && isActiveValue !== '') {\n      const isActiveStr = String(isActiveValue).toLowerCase().trim();\n      product.isActive = !['false', '0', 'no', 'n', 'inactive', 'disabled'].includes(isActiveStr);\n    } else {\n      product.isActive = true; // Default value\n    }\n    return {\n      product: errors.length === 0 ? product : null,\n      errors\n    };\n  } catch (error) {\n    errors.push(`Row ${rowIndex}: Error parsing data - ${error instanceof Error ? error.message : 'Unknown error'}`);\n    return {\n      product: null,\n      errors\n    };\n  }\n}\n\n/**\n * Parse Excel file and extract product data\n */\nexport async function parseExcelFile(file) {\n  try {\n    const arrayBuffer = await file.arrayBuffer();\n    const workbook = XLSX.read(arrayBuffer, {\n      type: 'array'\n    });\n\n    // Use the first worksheet\n    const firstSheetName = workbook.SheetNames[0];\n    if (!firstSheetName) {\n      return {\n        success: false,\n        errors: ['No worksheets found in the Excel file']\n      };\n    }\n    const worksheet = workbook.Sheets[firstSheetName];\n\n    // Convert to JSON with header row\n    const jsonData = XLSX.utils.sheet_to_json(worksheet, {\n      header: 1,\n      defval: '',\n      blankrows: false\n    });\n    if (jsonData.length === 0) {\n      return {\n        success: false,\n        errors: ['The Excel file appears to be empty']\n      };\n    }\n\n    // Extract headers from first row\n    const headers = jsonData[0].map(header => String(header).trim());\n    const normalizedHeaders = normalizeHeaders(headers);\n\n    // Validate headers\n    const headerValidation = validateHeaders(headers);\n    if (!headerValidation.isValid) {\n      return {\n        success: false,\n        errors: headerValidation.errors,\n        warnings: headerValidation.warnings\n      };\n    }\n\n    // Process data rows\n    const products = [];\n    const errors = [...headerValidation.errors];\n    const warnings = [...headerValidation.warnings];\n    for (let i = 1; i < jsonData.length; i++) {\n      const row = jsonData[i];\n\n      // Skip empty rows\n      if (row.every(cell => !cell || String(cell).trim() === '')) {\n        continue;\n      }\n\n      // Create row object with normalized headers\n      const rowData = {};\n      normalizedHeaders.forEach((header, index) => {\n        rowData[header] = row[index];\n      });\n\n      // Parse and validate the row\n      const {\n        product,\n        errors: rowErrors\n      } = parseProductRow(rowData, i + 1);\n      if (product) {\n        products.push(product);\n      }\n      errors.push(...rowErrors);\n    }\n    return {\n      success: errors.length === 0,\n      data: products,\n      errors: errors.length > 0 ? errors : undefined,\n      warnings: warnings.length > 0 ? warnings : undefined\n    };\n  } catch (error) {\n    return {\n      success: false,\n      errors: [`Failed to parse Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`]\n    };\n  }\n}\n\n/**\n * Parse CSV file and extract product data\n */\nexport async function parseCSVFile(file) {\n  try {\n    const text = await file.text();\n    const lines = text.split('\\n').filter(line => line.trim());\n    if (lines.length === 0) {\n      return {\n        success: false,\n        errors: ['The CSV file appears to be empty']\n      };\n    }\n\n    // Parse CSV headers\n    const headers = lines[0].split(',').map(h => h.replace(/\"/g, '').trim());\n    const normalizedHeaders = normalizeHeaders(headers);\n\n    // Validate headers\n    const headerValidation = validateHeaders(headers);\n    if (!headerValidation.isValid) {\n      return {\n        success: false,\n        errors: headerValidation.errors,\n        warnings: headerValidation.warnings\n      };\n    }\n\n    // Process data rows\n    const products = [];\n    const errors = [...headerValidation.errors];\n    const warnings = [...headerValidation.warnings];\n    for (let i = 1; i < lines.length; i++) {\n      const line = lines[i].trim();\n      if (!line) continue;\n\n      // Simple CSV parsing (handles quoted values)\n      const values = line.split(',').map(v => v.replace(/\"/g, '').trim());\n\n      // Create row object with normalized headers\n      const rowData = {};\n      normalizedHeaders.forEach((header, index) => {\n        rowData[header] = values[index] || '';\n      });\n\n      // Parse and validate the row\n      const {\n        product,\n        errors: rowErrors\n      } = parseProductRow(rowData, i + 1);\n      if (product) {\n        products.push(product);\n      }\n      errors.push(...rowErrors);\n    }\n    return {\n      success: errors.length === 0,\n      data: products,\n      errors: errors.length > 0 ? errors : undefined,\n      warnings: warnings.length > 0 ? warnings : undefined\n    };\n  } catch (error) {\n    return {\n      success: false,\n      errors: [`Failed to parse CSV file: ${error instanceof Error ? error.message : 'Unknown error'}`]\n    };\n  }\n}\n\n/**\n * Main function to parse import file (Excel or CSV)\n */\nexport async function parseImportFile(file) {\n  const fileName = file.name.toLowerCase();\n  if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {\n    return parseExcelFile(file);\n  } else if (fileName.endsWith('.csv')) {\n    return parseCSVFile(file);\n  } else {\n    return {\n      success: false,\n      errors: ['Unsupported file format. Please use Excel (.xlsx, .xls) or CSV (.csv) files.']\n    };\n  }\n}\n\n/**\n * Generate a sample Excel template for product import\n */\nexport function generateImportTemplate() {\n  const headers = ['Name', 'Description', 'Category', 'Price', 'Stock Quantity', 'Reorder Level', 'Has Expiry', 'Expiry Date', 'Is Active'];\n  const sampleData = [['A4 Paper (Ream)', '500 sheets of A4 paper', 'Paper', 450, 25, 10, 'No', '', 'Yes'], ['Blue Pen', 'Ballpoint pen - blue ink', 'Writing', 20, 50, 20, 'No', '', 'Yes'], ['Printer Ink Cartridge', 'Black ink cartridge for HP printers', 'Printer', 1200, 5, 3, 'Yes', '2025-12-31', 'Yes']];\n\n  // Create workbook and worksheet\n  const workbook = XLSX.utils.book_new();\n  const worksheet = XLSX.utils.aoa_to_sheet([headers, ...sampleData]);\n\n  // Add worksheet to workbook\n  XLSX.utils.book_append_sheet(workbook, worksheet, 'Products');\n\n  // Generate and download file\n  XLSX.writeFile(workbook, `product-import-template-${new Date().toISOString().split('T')[0]}.xlsx`);\n}", "map": {"version": 3, "names": ["XLSX", "REQUIRED_COLUMNS", "OPTIONAL_COLUMNS", "COLUMN_MAPPINGS", "normalizeHeaders", "headers", "map", "header", "normalized", "toLowerCase", "trim", "validateHeaders", "normalizedHeaders", "errors", "warnings", "missingRequired", "filter", "required", "includes", "length", "push", "join", "duplicates", "index", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "parseProductRow", "rowData", "rowIndex", "product", "name", "String", "category", "priceValue", "undefined", "price", "parseFloat", "replace", "isNaN", "stockValue", "stockQuantity", "parseInt", "description", "reorderValue", "reorderLevel", "hasExpiryValue", "hasExpiryStr", "hasEx<PERSON>ry", "expiryDateValue", "expiryDate", "SSF", "parse_date_code", "Date", "getTime", "error", "isActiveValue", "isActiveStr", "isActive", "Error", "message", "parseExcelFile", "file", "arrayBuffer", "workbook", "read", "type", "firstSheetName", "SheetNames", "success", "worksheet", "Sheets", "jsonData", "utils", "sheet_to_json", "defval", "blankrows", "headerValidation", "products", "i", "row", "every", "cell", "for<PERSON>ach", "rowErrors", "data", "parseCSVFile", "text", "lines", "split", "line", "h", "values", "v", "parseImportFile", "fileName", "endsWith", "generateImportTemplate", "sampleData", "book_new", "aoa_to_sheet", "book_append_sheet", "writeFile", "toISOString"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/utils/excelImport.ts"], "sourcesContent": ["import * as XLSX from 'xlsx';\nimport { Product } from '../types';\n\nexport interface ImportResult {\n  success: boolean;\n  data?: Partial<Product>[];\n  errors?: string[];\n  warnings?: string[];\n}\n\nexport interface ImportValidationResult {\n  isValid: boolean;\n  errors: string[];\n  warnings: string[];\n}\n\n// Required columns for product import\nconst REQUIRED_COLUMNS = ['Name', 'Category', 'Price', 'Stock Quantity'];\n\n// Optional columns with their default values\nconst OPTIONAL_COLUMNS = {\n  'Description': '',\n  'Reorder Level': 10,\n  'Has Expiry': false,\n  'Expiry Date': null,\n  'Is Active': true\n};\n\n// Column mapping for different naming conventions\nconst COLUMN_MAPPINGS: { [key: string]: string } = {\n  'product name': 'Name',\n  'product_name': 'Name',\n  'productname': 'Name',\n  'item name': 'Name',\n  'item_name': 'Name',\n  'itemname': 'Name',\n  'title': 'Name',\n  \n  'product category': 'Category',\n  'product_category': 'Category',\n  'productcategory': 'Category',\n  'item category': 'Category',\n  'item_category': 'Category',\n  'itemcategory': 'Category',\n  'cat': 'Category',\n  \n  'unit price': 'Price',\n  'unit_price': 'Price',\n  'unitprice': 'Price',\n  'cost': 'Price',\n  'amount': 'Price',\n  \n  'stock': 'Stock Quantity',\n  'quantity': 'Stock Quantity',\n  'qty': 'Stock Quantity',\n  'inventory': 'Stock Quantity',\n  'stock_quantity': 'Stock Quantity',\n  'stockquantity': 'Stock Quantity',\n  \n  'desc': 'Description',\n  'product description': 'Description',\n  'product_description': 'Description',\n  'productdescription': 'Description',\n  'item description': 'Description',\n  'item_description': 'Description',\n  'itemdescription': 'Description',\n  \n  'reorder': 'Reorder Level',\n  'reorder_level': 'Reorder Level',\n  'reorderlevel': 'Reorder Level',\n  'min stock': 'Reorder Level',\n  'min_stock': 'Reorder Level',\n  'minstock': 'Reorder Level',\n  'minimum': 'Reorder Level',\n  \n  'expiry': 'Has Expiry',\n  'has_expiry': 'Has Expiry',\n  'hasexpiry': 'Has Expiry',\n  'expires': 'Has Expiry',\n  'perishable': 'Has Expiry',\n  \n  'expiry_date': 'Expiry Date',\n  'expirydate': 'Expiry Date',\n  'expiration': 'Expiry Date',\n  'expiration_date': 'Expiry Date',\n  'expirationdate': 'Expiry Date',\n  'expires_on': 'Expiry Date',\n  'expireson': 'Expiry Date',\n  \n  'active': 'Is Active',\n  'is_active': 'Is Active',\n  'isactive': 'Is Active',\n  'enabled': 'Is Active',\n  'status': 'Is Active'\n};\n\n/**\n * Normalize column headers to match expected format\n */\nfunction normalizeHeaders(headers: string[]): string[] {\n  return headers.map(header => {\n    const normalized = header.toLowerCase().trim();\n    return COLUMN_MAPPINGS[normalized] || header;\n  });\n}\n\n/**\n * Validate that required columns are present\n */\nfunction validateHeaders(headers: string[]): ImportValidationResult {\n  const normalizedHeaders = normalizeHeaders(headers);\n  const errors: string[] = [];\n  const warnings: string[] = [];\n  \n  // Check for required columns\n  const missingRequired = REQUIRED_COLUMNS.filter(\n    required => !normalizedHeaders.includes(required)\n  );\n  \n  if (missingRequired.length > 0) {\n    errors.push(`Missing required columns: ${missingRequired.join(', ')}`);\n  }\n  \n  // Check for duplicate columns\n  const duplicates = normalizedHeaders.filter(\n    (header, index) => normalizedHeaders.indexOf(header) !== index\n  );\n  \n  if (duplicates.length > 0) {\n    warnings.push(`Duplicate columns found: ${duplicates.join(', ')}`);\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors,\n    warnings\n  };\n}\n\n/**\n * Parse and validate a single row of product data\n */\nfunction parseProductRow(\n  rowData: { [key: string]: any }, \n  rowIndex: number\n): { product: Partial<Product> | null; errors: string[] } {\n  const errors: string[] = [];\n  const product: Partial<Product> = {};\n  \n  try {\n    // Name (required)\n    const name = String(rowData['Name'] || '').trim();\n    if (!name) {\n      errors.push(`Row ${rowIndex}: Name is required`);\n    } else {\n      product.name = name;\n    }\n    \n    // Category (required)\n    const category = String(rowData['Category'] || '').trim();\n    if (!category) {\n      errors.push(`Row ${rowIndex}: Category is required`);\n    } else {\n      product.category = category;\n    }\n    \n    // Price (required)\n    const priceValue = rowData['Price'];\n    if (priceValue === undefined || priceValue === null || priceValue === '') {\n      errors.push(`Row ${rowIndex}: Price is required`);\n    } else {\n      const price = parseFloat(String(priceValue).replace(/[^\\d.-]/g, ''));\n      if (isNaN(price) || price < 0) {\n        errors.push(`Row ${rowIndex}: Price must be a valid positive number`);\n      } else {\n        product.price = price;\n      }\n    }\n    \n    // Stock Quantity (required)\n    const stockValue = rowData['Stock Quantity'];\n    if (stockValue === undefined || stockValue === null || stockValue === '') {\n      errors.push(`Row ${rowIndex}: Stock Quantity is required`);\n    } else {\n      const stockQuantity = parseInt(String(stockValue));\n      if (isNaN(stockQuantity) || stockQuantity < 0) {\n        errors.push(`Row ${rowIndex}: Stock Quantity must be a valid non-negative integer`);\n      } else {\n        product.stockQuantity = stockQuantity;\n      }\n    }\n    \n    // Description (optional)\n    const description = String(rowData['Description'] || '').trim();\n    product.description = description;\n    \n    // Reorder Level (optional)\n    const reorderValue = rowData['Reorder Level'];\n    if (reorderValue !== undefined && reorderValue !== null && reorderValue !== '') {\n      const reorderLevel = parseInt(String(reorderValue));\n      if (isNaN(reorderLevel) || reorderLevel < 0) {\n        errors.push(`Row ${rowIndex}: Reorder Level must be a valid non-negative integer`);\n      } else {\n        product.reorderLevel = reorderLevel;\n      }\n    } else {\n      product.reorderLevel = 10; // Default value\n    }\n    \n    // Has Expiry (optional)\n    const hasExpiryValue = rowData['Has Expiry'];\n    if (hasExpiryValue !== undefined && hasExpiryValue !== null && hasExpiryValue !== '') {\n      const hasExpiryStr = String(hasExpiryValue).toLowerCase().trim();\n      product.hasExpiry = ['true', '1', 'yes', 'y'].includes(hasExpiryStr);\n    } else {\n      product.hasExpiry = false;\n    }\n    \n    // Expiry Date (optional)\n    const expiryDateValue = rowData['Expiry Date'];\n    if (expiryDateValue && product.hasExpiry) {\n      try {\n        let expiryDate: Date;\n        \n        if (typeof expiryDateValue === 'number') {\n          // Excel date serial number\n          expiryDate = XLSX.SSF.parse_date_code(expiryDateValue);\n        } else {\n          // String date\n          expiryDate = new Date(String(expiryDateValue));\n        }\n        \n        if (isNaN(expiryDate.getTime())) {\n          errors.push(`Row ${rowIndex}: Invalid expiry date format`);\n        } else {\n          product.expiryDate = expiryDate;\n        }\n      } catch (error) {\n        errors.push(`Row ${rowIndex}: Invalid expiry date format`);\n      }\n    }\n    \n    // Is Active (optional)\n    const isActiveValue = rowData['Is Active'];\n    if (isActiveValue !== undefined && isActiveValue !== null && isActiveValue !== '') {\n      const isActiveStr = String(isActiveValue).toLowerCase().trim();\n      product.isActive = !['false', '0', 'no', 'n', 'inactive', 'disabled'].includes(isActiveStr);\n    } else {\n      product.isActive = true; // Default value\n    }\n    \n    return { product: errors.length === 0 ? product : null, errors };\n\n  } catch (error) {\n    errors.push(`Row ${rowIndex}: Error parsing data - ${error instanceof Error ? error.message : 'Unknown error'}`);\n    return { product: null, errors };\n  }\n}\n\n/**\n * Parse Excel file and extract product data\n */\nexport async function parseExcelFile(file: File): Promise<ImportResult> {\n  try {\n    const arrayBuffer = await file.arrayBuffer();\n    const workbook = XLSX.read(arrayBuffer, { type: 'array' });\n\n    // Use the first worksheet\n    const firstSheetName = workbook.SheetNames[0];\n    if (!firstSheetName) {\n      return {\n        success: false,\n        errors: ['No worksheets found in the Excel file']\n      };\n    }\n\n    const worksheet = workbook.Sheets[firstSheetName];\n\n    // Convert to JSON with header row\n    const jsonData = XLSX.utils.sheet_to_json(worksheet, {\n      header: 1,\n      defval: '',\n      blankrows: false\n    }) as any[][];\n\n    if (jsonData.length === 0) {\n      return {\n        success: false,\n        errors: ['The Excel file appears to be empty']\n      };\n    }\n\n    // Extract headers from first row\n    const headers = jsonData[0].map(header => String(header).trim());\n    const normalizedHeaders = normalizeHeaders(headers);\n\n    // Validate headers\n    const headerValidation = validateHeaders(headers);\n    if (!headerValidation.isValid) {\n      return {\n        success: false,\n        errors: headerValidation.errors,\n        warnings: headerValidation.warnings\n      };\n    }\n\n    // Process data rows\n    const products: Partial<Product>[] = [];\n    const errors: string[] = [...headerValidation.errors];\n    const warnings: string[] = [...headerValidation.warnings];\n\n    for (let i = 1; i < jsonData.length; i++) {\n      const row = jsonData[i];\n\n      // Skip empty rows\n      if (row.every(cell => !cell || String(cell).trim() === '')) {\n        continue;\n      }\n\n      // Create row object with normalized headers\n      const rowData: { [key: string]: any } = {};\n      normalizedHeaders.forEach((header, index) => {\n        rowData[header] = row[index];\n      });\n\n      // Parse and validate the row\n      const { product, errors: rowErrors } = parseProductRow(rowData, i + 1);\n\n      if (product) {\n        products.push(product);\n      }\n\n      errors.push(...rowErrors);\n    }\n\n    return {\n      success: errors.length === 0,\n      data: products,\n      errors: errors.length > 0 ? errors : undefined,\n      warnings: warnings.length > 0 ? warnings : undefined\n    };\n\n  } catch (error) {\n    return {\n      success: false,\n      errors: [`Failed to parse Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`]\n    };\n  }\n}\n\n/**\n * Parse CSV file and extract product data\n */\nexport async function parseCSVFile(file: File): Promise<ImportResult> {\n  try {\n    const text = await file.text();\n    const lines = text.split('\\n').filter(line => line.trim());\n\n    if (lines.length === 0) {\n      return {\n        success: false,\n        errors: ['The CSV file appears to be empty']\n      };\n    }\n\n    // Parse CSV headers\n    const headers = lines[0].split(',').map(h => h.replace(/\"/g, '').trim());\n    const normalizedHeaders = normalizeHeaders(headers);\n\n    // Validate headers\n    const headerValidation = validateHeaders(headers);\n    if (!headerValidation.isValid) {\n      return {\n        success: false,\n        errors: headerValidation.errors,\n        warnings: headerValidation.warnings\n      };\n    }\n\n    // Process data rows\n    const products: Partial<Product>[] = [];\n    const errors: string[] = [...headerValidation.errors];\n    const warnings: string[] = [...headerValidation.warnings];\n\n    for (let i = 1; i < lines.length; i++) {\n      const line = lines[i].trim();\n      if (!line) continue;\n\n      // Simple CSV parsing (handles quoted values)\n      const values = line.split(',').map(v => v.replace(/\"/g, '').trim());\n\n      // Create row object with normalized headers\n      const rowData: { [key: string]: any } = {};\n      normalizedHeaders.forEach((header, index) => {\n        rowData[header] = values[index] || '';\n      });\n\n      // Parse and validate the row\n      const { product, errors: rowErrors } = parseProductRow(rowData, i + 1);\n\n      if (product) {\n        products.push(product);\n      }\n\n      errors.push(...rowErrors);\n    }\n\n    return {\n      success: errors.length === 0,\n      data: products,\n      errors: errors.length > 0 ? errors : undefined,\n      warnings: warnings.length > 0 ? warnings : undefined\n    };\n\n  } catch (error) {\n    return {\n      success: false,\n      errors: [`Failed to parse CSV file: ${error instanceof Error ? error.message : 'Unknown error'}`]\n    };\n  }\n}\n\n/**\n * Main function to parse import file (Excel or CSV)\n */\nexport async function parseImportFile(file: File): Promise<ImportResult> {\n  const fileName = file.name.toLowerCase();\n\n  if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {\n    return parseExcelFile(file);\n  } else if (fileName.endsWith('.csv')) {\n    return parseCSVFile(file);\n  } else {\n    return {\n      success: false,\n      errors: ['Unsupported file format. Please use Excel (.xlsx, .xls) or CSV (.csv) files.']\n    };\n  }\n}\n\n/**\n * Generate a sample Excel template for product import\n */\nexport function generateImportTemplate(): void {\n  const headers = [\n    'Name',\n    'Description',\n    'Category',\n    'Price',\n    'Stock Quantity',\n    'Reorder Level',\n    'Has Expiry',\n    'Expiry Date',\n    'Is Active'\n  ];\n\n  const sampleData = [\n    [\n      'A4 Paper (Ream)',\n      '500 sheets of A4 paper',\n      'Paper',\n      450,\n      25,\n      10,\n      'No',\n      '',\n      'Yes'\n    ],\n    [\n      'Blue Pen',\n      'Ballpoint pen - blue ink',\n      'Writing',\n      20,\n      50,\n      20,\n      'No',\n      '',\n      'Yes'\n    ],\n    [\n      'Printer Ink Cartridge',\n      'Black ink cartridge for HP printers',\n      'Printer',\n      1200,\n      5,\n      3,\n      'Yes',\n      '2025-12-31',\n      'Yes'\n    ]\n  ];\n\n  // Create workbook and worksheet\n  const workbook = XLSX.utils.book_new();\n  const worksheet = XLSX.utils.aoa_to_sheet([headers, ...sampleData]);\n\n  // Add worksheet to workbook\n  XLSX.utils.book_append_sheet(workbook, worksheet, 'Products');\n\n  // Generate and download file\n  XLSX.writeFile(workbook, `product-import-template-${new Date().toISOString().split('T')[0]}.xlsx`);\n}\n"], "mappings": "AAAA,OAAO,KAAKA,IAAI,MAAM,MAAM;AAgB5B;AACA,MAAMC,gBAAgB,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,gBAAgB,CAAC;;AAExE;AACA,MAAMC,gBAAgB,GAAG;EACvB,aAAa,EAAE,EAAE;EACjB,eAAe,EAAE,EAAE;EACnB,YAAY,EAAE,KAAK;EACnB,aAAa,EAAE,IAAI;EACnB,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,eAA0C,GAAG;EACjD,cAAc,EAAE,MAAM;EACtB,cAAc,EAAE,MAAM;EACtB,aAAa,EAAE,MAAM;EACrB,WAAW,EAAE,MAAM;EACnB,WAAW,EAAE,MAAM;EACnB,UAAU,EAAE,MAAM;EAClB,OAAO,EAAE,MAAM;EAEf,kBAAkB,EAAE,UAAU;EAC9B,kBAAkB,EAAE,UAAU;EAC9B,iBAAiB,EAAE,UAAU;EAC7B,eAAe,EAAE,UAAU;EAC3B,eAAe,EAAE,UAAU;EAC3B,cAAc,EAAE,UAAU;EAC1B,KAAK,EAAE,UAAU;EAEjB,YAAY,EAAE,OAAO;EACrB,YAAY,EAAE,OAAO;EACrB,WAAW,EAAE,OAAO;EACpB,MAAM,EAAE,OAAO;EACf,QAAQ,EAAE,OAAO;EAEjB,OAAO,EAAE,gBAAgB;EACzB,UAAU,EAAE,gBAAgB;EAC5B,KAAK,EAAE,gBAAgB;EACvB,WAAW,EAAE,gBAAgB;EAC7B,gBAAgB,EAAE,gBAAgB;EAClC,eAAe,EAAE,gBAAgB;EAEjC,MAAM,EAAE,aAAa;EACrB,qBAAqB,EAAE,aAAa;EACpC,qBAAqB,EAAE,aAAa;EACpC,oBAAoB,EAAE,aAAa;EACnC,kBAAkB,EAAE,aAAa;EACjC,kBAAkB,EAAE,aAAa;EACjC,iBAAiB,EAAE,aAAa;EAEhC,SAAS,EAAE,eAAe;EAC1B,eAAe,EAAE,eAAe;EAChC,cAAc,EAAE,eAAe;EAC/B,WAAW,EAAE,eAAe;EAC5B,WAAW,EAAE,eAAe;EAC5B,UAAU,EAAE,eAAe;EAC3B,SAAS,EAAE,eAAe;EAE1B,QAAQ,EAAE,YAAY;EACtB,YAAY,EAAE,YAAY;EAC1B,WAAW,EAAE,YAAY;EACzB,SAAS,EAAE,YAAY;EACvB,YAAY,EAAE,YAAY;EAE1B,aAAa,EAAE,aAAa;EAC5B,YAAY,EAAE,aAAa;EAC3B,YAAY,EAAE,aAAa;EAC3B,iBAAiB,EAAE,aAAa;EAChC,gBAAgB,EAAE,aAAa;EAC/B,YAAY,EAAE,aAAa;EAC3B,WAAW,EAAE,aAAa;EAE1B,QAAQ,EAAE,WAAW;EACrB,WAAW,EAAE,WAAW;EACxB,UAAU,EAAE,WAAW;EACvB,SAAS,EAAE,WAAW;EACtB,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,OAAiB,EAAY;EACrD,OAAOA,OAAO,CAACC,GAAG,CAACC,MAAM,IAAI;IAC3B,MAAMC,UAAU,GAAGD,MAAM,CAACE,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;IAC9C,OAAOP,eAAe,CAACK,UAAU,CAAC,IAAID,MAAM;EAC9C,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,SAASI,eAAeA,CAACN,OAAiB,EAA0B;EAClE,MAAMO,iBAAiB,GAAGR,gBAAgB,CAACC,OAAO,CAAC;EACnD,MAAMQ,MAAgB,GAAG,EAAE;EAC3B,MAAMC,QAAkB,GAAG,EAAE;;EAE7B;EACA,MAAMC,eAAe,GAAGd,gBAAgB,CAACe,MAAM,CAC7CC,QAAQ,IAAI,CAACL,iBAAiB,CAACM,QAAQ,CAACD,QAAQ,CAClD,CAAC;EAED,IAAIF,eAAe,CAACI,MAAM,GAAG,CAAC,EAAE;IAC9BN,MAAM,CAACO,IAAI,CAAC,6BAA6BL,eAAe,CAACM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACxE;;EAEA;EACA,MAAMC,UAAU,GAAGV,iBAAiB,CAACI,MAAM,CACzC,CAACT,MAAM,EAAEgB,KAAK,KAAKX,iBAAiB,CAACY,OAAO,CAACjB,MAAM,CAAC,KAAKgB,KAC3D,CAAC;EAED,IAAID,UAAU,CAACH,MAAM,GAAG,CAAC,EAAE;IACzBL,QAAQ,CAACM,IAAI,CAAC,4BAA4BE,UAAU,CAACD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACpE;EAEA,OAAO;IACLI,OAAO,EAAEZ,MAAM,CAACM,MAAM,KAAK,CAAC;IAC5BN,MAAM;IACNC;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA,SAASY,eAAeA,CACtBC,OAA+B,EAC/BC,QAAgB,EACwC;EACxD,MAAMf,MAAgB,GAAG,EAAE;EAC3B,MAAMgB,OAAyB,GAAG,CAAC,CAAC;EAEpC,IAAI;IACF;IACA,MAAMC,IAAI,GAAGC,MAAM,CAACJ,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAACjB,IAAI,CAAC,CAAC;IACjD,IAAI,CAACoB,IAAI,EAAE;MACTjB,MAAM,CAACO,IAAI,CAAC,OAAOQ,QAAQ,oBAAoB,CAAC;IAClD,CAAC,MAAM;MACLC,OAAO,CAACC,IAAI,GAAGA,IAAI;IACrB;;IAEA;IACA,MAAME,QAAQ,GAAGD,MAAM,CAACJ,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAACjB,IAAI,CAAC,CAAC;IACzD,IAAI,CAACsB,QAAQ,EAAE;MACbnB,MAAM,CAACO,IAAI,CAAC,OAAOQ,QAAQ,wBAAwB,CAAC;IACtD,CAAC,MAAM;MACLC,OAAO,CAACG,QAAQ,GAAGA,QAAQ;IAC7B;;IAEA;IACA,MAAMC,UAAU,GAAGN,OAAO,CAAC,OAAO,CAAC;IACnC,IAAIM,UAAU,KAAKC,SAAS,IAAID,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,EAAE,EAAE;MACxEpB,MAAM,CAACO,IAAI,CAAC,OAAOQ,QAAQ,qBAAqB,CAAC;IACnD,CAAC,MAAM;MACL,MAAMO,KAAK,GAAGC,UAAU,CAACL,MAAM,CAACE,UAAU,CAAC,CAACI,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;MACpE,IAAIC,KAAK,CAACH,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;QAC7BtB,MAAM,CAACO,IAAI,CAAC,OAAOQ,QAAQ,yCAAyC,CAAC;MACvE,CAAC,MAAM;QACLC,OAAO,CAACM,KAAK,GAAGA,KAAK;MACvB;IACF;;IAEA;IACA,MAAMI,UAAU,GAAGZ,OAAO,CAAC,gBAAgB,CAAC;IAC5C,IAAIY,UAAU,KAAKL,SAAS,IAAIK,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,EAAE,EAAE;MACxE1B,MAAM,CAACO,IAAI,CAAC,OAAOQ,QAAQ,8BAA8B,CAAC;IAC5D,CAAC,MAAM;MACL,MAAMY,aAAa,GAAGC,QAAQ,CAACV,MAAM,CAACQ,UAAU,CAAC,CAAC;MAClD,IAAID,KAAK,CAACE,aAAa,CAAC,IAAIA,aAAa,GAAG,CAAC,EAAE;QAC7C3B,MAAM,CAACO,IAAI,CAAC,OAAOQ,QAAQ,uDAAuD,CAAC;MACrF,CAAC,MAAM;QACLC,OAAO,CAACW,aAAa,GAAGA,aAAa;MACvC;IACF;;IAEA;IACA,MAAME,WAAW,GAAGX,MAAM,CAACJ,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAACjB,IAAI,CAAC,CAAC;IAC/DmB,OAAO,CAACa,WAAW,GAAGA,WAAW;;IAEjC;IACA,MAAMC,YAAY,GAAGhB,OAAO,CAAC,eAAe,CAAC;IAC7C,IAAIgB,YAAY,KAAKT,SAAS,IAAIS,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,EAAE,EAAE;MAC9E,MAAMC,YAAY,GAAGH,QAAQ,CAACV,MAAM,CAACY,YAAY,CAAC,CAAC;MACnD,IAAIL,KAAK,CAACM,YAAY,CAAC,IAAIA,YAAY,GAAG,CAAC,EAAE;QAC3C/B,MAAM,CAACO,IAAI,CAAC,OAAOQ,QAAQ,sDAAsD,CAAC;MACpF,CAAC,MAAM;QACLC,OAAO,CAACe,YAAY,GAAGA,YAAY;MACrC;IACF,CAAC,MAAM;MACLf,OAAO,CAACe,YAAY,GAAG,EAAE,CAAC,CAAC;IAC7B;;IAEA;IACA,MAAMC,cAAc,GAAGlB,OAAO,CAAC,YAAY,CAAC;IAC5C,IAAIkB,cAAc,KAAKX,SAAS,IAAIW,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,EAAE,EAAE;MACpF,MAAMC,YAAY,GAAGf,MAAM,CAACc,cAAc,CAAC,CAACpC,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;MAChEmB,OAAO,CAACkB,SAAS,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC7B,QAAQ,CAAC4B,YAAY,CAAC;IACtE,CAAC,MAAM;MACLjB,OAAO,CAACkB,SAAS,GAAG,KAAK;IAC3B;;IAEA;IACA,MAAMC,eAAe,GAAGrB,OAAO,CAAC,aAAa,CAAC;IAC9C,IAAIqB,eAAe,IAAInB,OAAO,CAACkB,SAAS,EAAE;MACxC,IAAI;QACF,IAAIE,UAAgB;QAEpB,IAAI,OAAOD,eAAe,KAAK,QAAQ,EAAE;UACvC;UACAC,UAAU,GAAGjD,IAAI,CAACkD,GAAG,CAACC,eAAe,CAACH,eAAe,CAAC;QACxD,CAAC,MAAM;UACL;UACAC,UAAU,GAAG,IAAIG,IAAI,CAACrB,MAAM,CAACiB,eAAe,CAAC,CAAC;QAChD;QAEA,IAAIV,KAAK,CAACW,UAAU,CAACI,OAAO,CAAC,CAAC,CAAC,EAAE;UAC/BxC,MAAM,CAACO,IAAI,CAAC,OAAOQ,QAAQ,8BAA8B,CAAC;QAC5D,CAAC,MAAM;UACLC,OAAO,CAACoB,UAAU,GAAGA,UAAU;QACjC;MACF,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdzC,MAAM,CAACO,IAAI,CAAC,OAAOQ,QAAQ,8BAA8B,CAAC;MAC5D;IACF;;IAEA;IACA,MAAM2B,aAAa,GAAG5B,OAAO,CAAC,WAAW,CAAC;IAC1C,IAAI4B,aAAa,KAAKrB,SAAS,IAAIqB,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,EAAE,EAAE;MACjF,MAAMC,WAAW,GAAGzB,MAAM,CAACwB,aAAa,CAAC,CAAC9C,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;MAC9DmB,OAAO,CAAC4B,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,CAAC,CAACvC,QAAQ,CAACsC,WAAW,CAAC;IAC7F,CAAC,MAAM;MACL3B,OAAO,CAAC4B,QAAQ,GAAG,IAAI,CAAC,CAAC;IAC3B;IAEA,OAAO;MAAE5B,OAAO,EAAEhB,MAAM,CAACM,MAAM,KAAK,CAAC,GAAGU,OAAO,GAAG,IAAI;MAAEhB;IAAO,CAAC;EAElE,CAAC,CAAC,OAAOyC,KAAK,EAAE;IACdzC,MAAM,CAACO,IAAI,CAAC,OAAOQ,QAAQ,0BAA0B0B,KAAK,YAAYI,KAAK,GAAGJ,KAAK,CAACK,OAAO,GAAG,eAAe,EAAE,CAAC;IAChH,OAAO;MAAE9B,OAAO,EAAE,IAAI;MAAEhB;IAAO,CAAC;EAClC;AACF;;AAEA;AACA;AACA;AACA,OAAO,eAAe+C,cAAcA,CAACC,IAAU,EAAyB;EACtE,IAAI;IACF,MAAMC,WAAW,GAAG,MAAMD,IAAI,CAACC,WAAW,CAAC,CAAC;IAC5C,MAAMC,QAAQ,GAAG/D,IAAI,CAACgE,IAAI,CAACF,WAAW,EAAE;MAAEG,IAAI,EAAE;IAAQ,CAAC,CAAC;;IAE1D;IACA,MAAMC,cAAc,GAAGH,QAAQ,CAACI,UAAU,CAAC,CAAC,CAAC;IAC7C,IAAI,CAACD,cAAc,EAAE;MACnB,OAAO;QACLE,OAAO,EAAE,KAAK;QACdvD,MAAM,EAAE,CAAC,uCAAuC;MAClD,CAAC;IACH;IAEA,MAAMwD,SAAS,GAAGN,QAAQ,CAACO,MAAM,CAACJ,cAAc,CAAC;;IAEjD;IACA,MAAMK,QAAQ,GAAGvE,IAAI,CAACwE,KAAK,CAACC,aAAa,CAACJ,SAAS,EAAE;MACnD9D,MAAM,EAAE,CAAC;MACTmE,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE;IACb,CAAC,CAAY;IAEb,IAAIJ,QAAQ,CAACpD,MAAM,KAAK,CAAC,EAAE;MACzB,OAAO;QACLiD,OAAO,EAAE,KAAK;QACdvD,MAAM,EAAE,CAAC,oCAAoC;MAC/C,CAAC;IACH;;IAEA;IACA,MAAMR,OAAO,GAAGkE,QAAQ,CAAC,CAAC,CAAC,CAACjE,GAAG,CAACC,MAAM,IAAIwB,MAAM,CAACxB,MAAM,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC;IAChE,MAAME,iBAAiB,GAAGR,gBAAgB,CAACC,OAAO,CAAC;;IAEnD;IACA,MAAMuE,gBAAgB,GAAGjE,eAAe,CAACN,OAAO,CAAC;IACjD,IAAI,CAACuE,gBAAgB,CAACnD,OAAO,EAAE;MAC7B,OAAO;QACL2C,OAAO,EAAE,KAAK;QACdvD,MAAM,EAAE+D,gBAAgB,CAAC/D,MAAM;QAC/BC,QAAQ,EAAE8D,gBAAgB,CAAC9D;MAC7B,CAAC;IACH;;IAEA;IACA,MAAM+D,QAA4B,GAAG,EAAE;IACvC,MAAMhE,MAAgB,GAAG,CAAC,GAAG+D,gBAAgB,CAAC/D,MAAM,CAAC;IACrD,MAAMC,QAAkB,GAAG,CAAC,GAAG8D,gBAAgB,CAAC9D,QAAQ,CAAC;IAEzD,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,QAAQ,CAACpD,MAAM,EAAE2D,CAAC,EAAE,EAAE;MACxC,MAAMC,GAAG,GAAGR,QAAQ,CAACO,CAAC,CAAC;;MAEvB;MACA,IAAIC,GAAG,CAACC,KAAK,CAACC,IAAI,IAAI,CAACA,IAAI,IAAIlD,MAAM,CAACkD,IAAI,CAAC,CAACvE,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE;QAC1D;MACF;;MAEA;MACA,MAAMiB,OAA+B,GAAG,CAAC,CAAC;MAC1Cf,iBAAiB,CAACsE,OAAO,CAAC,CAAC3E,MAAM,EAAEgB,KAAK,KAAK;QAC3CI,OAAO,CAACpB,MAAM,CAAC,GAAGwE,GAAG,CAACxD,KAAK,CAAC;MAC9B,CAAC,CAAC;;MAEF;MACA,MAAM;QAAEM,OAAO;QAAEhB,MAAM,EAAEsE;MAAU,CAAC,GAAGzD,eAAe,CAACC,OAAO,EAAEmD,CAAC,GAAG,CAAC,CAAC;MAEtE,IAAIjD,OAAO,EAAE;QACXgD,QAAQ,CAACzD,IAAI,CAACS,OAAO,CAAC;MACxB;MAEAhB,MAAM,CAACO,IAAI,CAAC,GAAG+D,SAAS,CAAC;IAC3B;IAEA,OAAO;MACLf,OAAO,EAAEvD,MAAM,CAACM,MAAM,KAAK,CAAC;MAC5BiE,IAAI,EAAEP,QAAQ;MACdhE,MAAM,EAAEA,MAAM,CAACM,MAAM,GAAG,CAAC,GAAGN,MAAM,GAAGqB,SAAS;MAC9CpB,QAAQ,EAAEA,QAAQ,CAACK,MAAM,GAAG,CAAC,GAAGL,QAAQ,GAAGoB;IAC7C,CAAC;EAEH,CAAC,CAAC,OAAOoB,KAAK,EAAE;IACd,OAAO;MACLc,OAAO,EAAE,KAAK;MACdvD,MAAM,EAAE,CAAC,+BAA+ByC,KAAK,YAAYI,KAAK,GAAGJ,KAAK,CAACK,OAAO,GAAG,eAAe,EAAE;IACpG,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA,OAAO,eAAe0B,YAAYA,CAACxB,IAAU,EAAyB;EACpE,IAAI;IACF,MAAMyB,IAAI,GAAG,MAAMzB,IAAI,CAACyB,IAAI,CAAC,CAAC;IAC9B,MAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,IAAI,CAAC,CAACxE,MAAM,CAACyE,IAAI,IAAIA,IAAI,CAAC/E,IAAI,CAAC,CAAC,CAAC;IAE1D,IAAI6E,KAAK,CAACpE,MAAM,KAAK,CAAC,EAAE;MACtB,OAAO;QACLiD,OAAO,EAAE,KAAK;QACdvD,MAAM,EAAE,CAAC,kCAAkC;MAC7C,CAAC;IACH;;IAEA;IACA,MAAMR,OAAO,GAAGkF,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAClF,GAAG,CAACoF,CAAC,IAAIA,CAAC,CAACrD,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC3B,IAAI,CAAC,CAAC,CAAC;IACxE,MAAME,iBAAiB,GAAGR,gBAAgB,CAACC,OAAO,CAAC;;IAEnD;IACA,MAAMuE,gBAAgB,GAAGjE,eAAe,CAACN,OAAO,CAAC;IACjD,IAAI,CAACuE,gBAAgB,CAACnD,OAAO,EAAE;MAC7B,OAAO;QACL2C,OAAO,EAAE,KAAK;QACdvD,MAAM,EAAE+D,gBAAgB,CAAC/D,MAAM;QAC/BC,QAAQ,EAAE8D,gBAAgB,CAAC9D;MAC7B,CAAC;IACH;;IAEA;IACA,MAAM+D,QAA4B,GAAG,EAAE;IACvC,MAAMhE,MAAgB,GAAG,CAAC,GAAG+D,gBAAgB,CAAC/D,MAAM,CAAC;IACrD,MAAMC,QAAkB,GAAG,CAAC,GAAG8D,gBAAgB,CAAC9D,QAAQ,CAAC;IAEzD,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,KAAK,CAACpE,MAAM,EAAE2D,CAAC,EAAE,EAAE;MACrC,MAAMW,IAAI,GAAGF,KAAK,CAACT,CAAC,CAAC,CAACpE,IAAI,CAAC,CAAC;MAC5B,IAAI,CAAC+E,IAAI,EAAE;;MAEX;MACA,MAAME,MAAM,GAAGF,IAAI,CAACD,KAAK,CAAC,GAAG,CAAC,CAAClF,GAAG,CAACsF,CAAC,IAAIA,CAAC,CAACvD,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC3B,IAAI,CAAC,CAAC,CAAC;;MAEnE;MACA,MAAMiB,OAA+B,GAAG,CAAC,CAAC;MAC1Cf,iBAAiB,CAACsE,OAAO,CAAC,CAAC3E,MAAM,EAAEgB,KAAK,KAAK;QAC3CI,OAAO,CAACpB,MAAM,CAAC,GAAGoF,MAAM,CAACpE,KAAK,CAAC,IAAI,EAAE;MACvC,CAAC,CAAC;;MAEF;MACA,MAAM;QAAEM,OAAO;QAAEhB,MAAM,EAAEsE;MAAU,CAAC,GAAGzD,eAAe,CAACC,OAAO,EAAEmD,CAAC,GAAG,CAAC,CAAC;MAEtE,IAAIjD,OAAO,EAAE;QACXgD,QAAQ,CAACzD,IAAI,CAACS,OAAO,CAAC;MACxB;MAEAhB,MAAM,CAACO,IAAI,CAAC,GAAG+D,SAAS,CAAC;IAC3B;IAEA,OAAO;MACLf,OAAO,EAAEvD,MAAM,CAACM,MAAM,KAAK,CAAC;MAC5BiE,IAAI,EAAEP,QAAQ;MACdhE,MAAM,EAAEA,MAAM,CAACM,MAAM,GAAG,CAAC,GAAGN,MAAM,GAAGqB,SAAS;MAC9CpB,QAAQ,EAAEA,QAAQ,CAACK,MAAM,GAAG,CAAC,GAAGL,QAAQ,GAAGoB;IAC7C,CAAC;EAEH,CAAC,CAAC,OAAOoB,KAAK,EAAE;IACd,OAAO;MACLc,OAAO,EAAE,KAAK;MACdvD,MAAM,EAAE,CAAC,6BAA6ByC,KAAK,YAAYI,KAAK,GAAGJ,KAAK,CAACK,OAAO,GAAG,eAAe,EAAE;IAClG,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA,OAAO,eAAekC,eAAeA,CAAChC,IAAU,EAAyB;EACvE,MAAMiC,QAAQ,GAAGjC,IAAI,CAAC/B,IAAI,CAACrB,WAAW,CAAC,CAAC;EAExC,IAAIqF,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC3D,OAAOnC,cAAc,CAACC,IAAI,CAAC;EAC7B,CAAC,MAAM,IAAIiC,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;IACpC,OAAOV,YAAY,CAACxB,IAAI,CAAC;EAC3B,CAAC,MAAM;IACL,OAAO;MACLO,OAAO,EAAE,KAAK;MACdvD,MAAM,EAAE,CAAC,8EAA8E;IACzF,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA,OAAO,SAASmF,sBAAsBA,CAAA,EAAS;EAC7C,MAAM3F,OAAO,GAAG,CACd,MAAM,EACN,aAAa,EACb,UAAU,EACV,OAAO,EACP,gBAAgB,EAChB,eAAe,EACf,YAAY,EACZ,aAAa,EACb,WAAW,CACZ;EAED,MAAM4F,UAAU,GAAG,CACjB,CACE,iBAAiB,EACjB,wBAAwB,EACxB,OAAO,EACP,GAAG,EACH,EAAE,EACF,EAAE,EACF,IAAI,EACJ,EAAE,EACF,KAAK,CACN,EACD,CACE,UAAU,EACV,0BAA0B,EAC1B,SAAS,EACT,EAAE,EACF,EAAE,EACF,EAAE,EACF,IAAI,EACJ,EAAE,EACF,KAAK,CACN,EACD,CACE,uBAAuB,EACvB,qCAAqC,EACrC,SAAS,EACT,IAAI,EACJ,CAAC,EACD,CAAC,EACD,KAAK,EACL,YAAY,EACZ,KAAK,CACN,CACF;;EAED;EACA,MAAMlC,QAAQ,GAAG/D,IAAI,CAACwE,KAAK,CAAC0B,QAAQ,CAAC,CAAC;EACtC,MAAM7B,SAAS,GAAGrE,IAAI,CAACwE,KAAK,CAAC2B,YAAY,CAAC,CAAC9F,OAAO,EAAE,GAAG4F,UAAU,CAAC,CAAC;;EAEnE;EACAjG,IAAI,CAACwE,KAAK,CAAC4B,iBAAiB,CAACrC,QAAQ,EAAEM,SAAS,EAAE,UAAU,CAAC;;EAE7D;EACArE,IAAI,CAACqG,SAAS,CAACtC,QAAQ,EAAE,2BAA2B,IAAIX,IAAI,CAAC,CAAC,CAACkD,WAAW,CAAC,CAAC,CAACd,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AACpG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}